<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RGSMDIA Player - Películas</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="movies-body">
    <header class="header">
        <div class="header-content">
            <button id="backBtn" class="back-btn">← Volver</button>
            <h1>Películas</h1>
        </div>
    </header>
    
    <div class="movies-layout">
        <aside class="sidebar">
            <h3>Categorías</h3>
            <div id="categoriesList" class="categories-list">
                <!-- Categorías se cargan dinámicamente -->
            </div>
        </aside>
        
        <main class="content">
            <div class="movies-section">
                <h3 id="categoryTitle">Selecciona una categoría</h3>
                <div id="moviesList" class="movies-grid">
                    <!-- Películas se cargan dinámicamente -->
                </div>
            </div>
        </main>
    </div>
    
    <!-- Modal del reproductor -->
    <div id="playerModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="movieTitle">Título de la Película</h3>
                <button id="closeModal" class="close-btn">×</button>
            </div>
            <div class="modal-body">
                <div id="videoPlayer" class="video-player">
                    <video id="mainVideo" controls>
                        Tu navegador no soporta el elemento video.
                    </video>
                </div>
                <div class="player-controls">
                    <button id="fullscreenBtn">🔳 Pantalla Completa</button>
                    <button id="pipBtn">📺 Picture-in-Picture</button>
                    <div class="volume-control">
                        <span>🔊</span>
                        <input type="range" id="volumeSlider" min="0" max="100" value="50">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/api.js"></script>
    <script src="js/player.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Verificar autenticación
        if (!localStorage.getItem('authToken')) {
            window.location.href = 'index.html';
        }
        
        // Navegación
        document.getElementById('backBtn').addEventListener('click', () => {
            window.location.href = 'home.html';
        });
        
        // Inicializar página
        document.addEventListener('DOMContentLoaded', () => {
            loadMovieCategories();
        });

        // Cargar categorías de películas
        async function loadMovieCategories() {
            try {
                const categories = await getVodCategories();
                displayCategories(categories);
            } catch (error) {
                console.error('Error loading movie categories:', error);
            }
        }

        // Mostrar categorías
        function displayCategories(categories) {
            const container = document.getElementById('categoriesList');
            if (!container) return;

            container.innerHTML = categories.map(cat => `
                <div class="category-item" data-id="${cat.category_id}" onclick="loadMovies(${cat.category_id}, '${cat.category_name}')">
                    ${cat.category_name}
                </div>
            `).join('');
        }

        // Cargar películas por categoría
        async function loadMovies(categoryId, categoryName) {
            try {
                document.getElementById('categoryTitle').textContent = categoryName;

                // Marcar categoría activa
                document.querySelectorAll('.category-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-id="${categoryId}"]`).classList.add('active');

                const movies = await getVodStreams(categoryId);
                displayMovies(movies);
            } catch (error) {
                console.error('Error loading movies:', error);
            }
        }

        // Mostrar películas
        function displayMovies(movies) {
            const container = document.getElementById('moviesList');
            if (!container) return;

            container.innerHTML = movies.map(movie => `
                <div class="movie-item" onclick="playMovie(${movie.stream_id}, '${movie.name}')">
                    <div class="movie-poster">🎬</div>
                    <div class="movie-title">${movie.name}</div>
                    <div class="movie-year">${movie.year || ''}</div>
                </div>
            `).join('');
        }

        // Reproducir película
        function playMovie(streamId, movieTitle) {
            const streamUrl = getVodUrl(streamId);

            // Mostrar modal del reproductor
            const modal = document.getElementById('playerModal');
            document.getElementById('movieTitle').textContent = movieTitle;
            modal.classList.remove('hidden');

            // Reproducir
            playStream(streamUrl, movieTitle, streamId);
        }

        // Cerrar modal
        document.getElementById('closeModal').addEventListener('click', () => {
            const modal = document.getElementById('playerModal');
            modal.classList.add('hidden');

            // Detener reproductor
            if (currentPlayer) {
                destroyPlayer();
            }
        });
    </script>
</body>
</html>
