<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RGSMDIA Player - Series</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="series-body">
    <header class="header">
        <div class="header-content">
            <button id="backBtn" class="back-btn">← Volver</button>
            <h1>Series</h1>
            <div class="breadcrumb" id="breadcrumb"></div>
        </div>
    </header>
    
    <div class="series-layout">
        <aside class="sidebar">
            <h3>Categorías</h3>
            <div id="categoriesList" class="categories-list">
                <!-- Categorías se cargan dinámicamente -->
            </div>
        </aside>
        
        <main class="content">
            <div class="series-section">
                <h3 id="sectionTitle">Selecciona una categoría</h3>
                <div id="contentContainer" class="content-container">
                    <!-- Contenido dinámico: series, temporadas o episodios -->
                </div>
            </div>
        </main>
    </div>
    
    <!-- Modal del reproductor -->
    <div id="playerModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="episodeTitle">Título del Episodio</h3>
                <button id="closeModal" class="close-btn">×</button>
            </div>
            <div class="modal-body">
                <div id="videoPlayer" class="video-player">
                    <video id="mainVideo" controls>
                        Tu navegador no soporta el elemento video.
                    </video>
                </div>
                <div class="player-controls">
                    <button id="fullscreenBtn">🔳 Pantalla Completa</button>
                    <button id="pipBtn">📺 Picture-in-Picture</button>
                    <div class="volume-control">
                        <span>🔊</span>
                        <input type="range" id="volumeSlider" min="0" max="100" value="50">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/api.js"></script>
    <script src="js/player.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Verificar autenticación
        if (!localStorage.getItem('authToken')) {
            window.location.href = 'index.html';
        }
        
        // Estado de navegación
        let currentView = 'categories'; // categories, series, seasons, episodes
        let currentData = {};
        
        // Navegación
        document.getElementById('backBtn').addEventListener('click', () => {
            if (currentView === 'categories') {
                window.location.href = 'home.html';
            } else {
                navigateBack();
            }
        });
        
        // Inicializar página
        document.addEventListener('DOMContentLoaded', () => {
            loadSeriesCategories();
        });

        // Cargar categorías de series
        async function loadSeriesCategories() {
            try {
                const categories = await getSeriesCategories();
                displayCategories(categories);
            } catch (error) {
                console.error('Error loading series categories:', error);
            }
        }

        // Mostrar categorías
        function displayCategories(categories) {
            const container = document.getElementById('categoriesList');
            if (!container) return;

            container.innerHTML = categories.map(cat => `
                <div class="category-item" data-id="${cat.category_id}" onclick="loadSeries(${cat.category_id}, '${cat.category_name}')">
                    ${cat.category_name}
                </div>
            `).join('');
        }

        // Cargar series por categoría
        async function loadSeries(categoryId, categoryName) {
            try {
                currentView = 'series';
                currentData = { categoryId, categoryName };

                document.getElementById('sectionTitle').textContent = categoryName;
                updateBreadcrumb([{ text: 'Categorías', action: () => loadSeriesCategories() }, { text: categoryName }]);

                // Marcar categoría activa
                document.querySelectorAll('.category-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-id="${categoryId}"]`).classList.add('active');

                const series = await getSeries(categoryId);
                displaySeriesList(series);
            } catch (error) {
                console.error('Error loading series:', error);
            }
        }

        // Mostrar lista de series
        function displaySeriesList(series) {
            const container = document.getElementById('contentContainer');
            if (!container) return;

            container.innerHTML = `
                <div class="series-grid">
                    ${series.map(serie => `
                        <div class="series-item" onclick="loadSeasons(${serie.series_id}, '${serie.name}')">
                            <div class="series-poster">📺</div>
                            <div class="series-title">${serie.name}</div>
                            <div class="series-year">${serie.year || ''}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Cargar temporadas
        async function loadSeasons(seriesId, seriesName) {
            try {
                currentView = 'seasons';
                currentData = { ...currentData, seriesId, seriesName };

                document.getElementById('sectionTitle').textContent = `${seriesName} - Temporadas`;
                updateBreadcrumb([
                    { text: 'Categorías', action: () => loadSeriesCategories() },
                    { text: currentData.categoryName, action: () => loadSeries(currentData.categoryId, currentData.categoryName) },
                    { text: seriesName }
                ]);

                const seriesInfo = await getSeriesInfo(seriesId);
                displaySeasons(seriesInfo);
            } catch (error) {
                console.error('Error loading seasons:', error);
            }
        }

        // Mostrar temporadas
        function displaySeasons(seriesInfo) {
            const container = document.getElementById('contentContainer');
            if (!container || !seriesInfo.seasons) return;

            const seasons = Object.keys(seriesInfo.seasons);

            container.innerHTML = `
                <div class="seasons-grid">
                    ${seasons.map(seasonNum => `
                        <div class="season-item" onclick="loadEpisodes(${seasonNum})">
                            <div class="season-poster">📁</div>
                            <div class="season-title">Temporada ${seasonNum}</div>
                            <div class="season-episodes">${Object.keys(seriesInfo.seasons[seasonNum]).length} episodios</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Cargar episodios
        async function loadEpisodes(seasonNumber) {
            try {
                currentView = 'episodes';
                currentData = { ...currentData, seasonNumber };

                document.getElementById('sectionTitle').textContent = `${currentData.seriesName} - Temporada ${seasonNumber}`;
                updateBreadcrumb([
                    { text: 'Categorías', action: () => loadSeriesCategories() },
                    { text: currentData.categoryName, action: () => loadSeries(currentData.categoryId, currentData.categoryName) },
                    { text: currentData.seriesName, action: () => loadSeasons(currentData.seriesId, currentData.seriesName) },
                    { text: `Temporada ${seasonNumber}` }
                ]);

                const seriesInfo = await getSeriesInfo(currentData.seriesId);
                displayEpisodes(seriesInfo.seasons[seasonNumber]);
            } catch (error) {
                console.error('Error loading episodes:', error);
            }
        }

        // Mostrar episodios
        function displayEpisodes(episodes) {
            const container = document.getElementById('contentContainer');
            if (!container) return;

            const episodesList = Object.values(episodes);

            container.innerHTML = `
                <div class="episodes-grid">
                    ${episodesList.map(episode => `
                        <div class="episode-item" onclick="playEpisode(${episode.id}, '${episode.title}')">
                            <div class="episode-poster">▶️</div>
                            <div class="episode-title">${episode.title}</div>
                            <div class="episode-info">Episodio ${episode.episode_num}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Reproducir episodio
        function playEpisode(episodeId, episodeTitle) {
            const streamUrl = getSeriesUrl(episodeId);

            // Mostrar modal del reproductor
            const modal = document.getElementById('playerModal');
            document.getElementById('episodeTitle').textContent = episodeTitle;
            modal.classList.remove('hidden');

            // Reproducir
            playStream(streamUrl, episodeTitle, episodeId);
        }

        // Navegación hacia atrás
        function navigateBack() {
            switch (currentView) {
                case 'episodes':
                    loadSeasons(currentData.seriesId, currentData.seriesName);
                    break;
                case 'seasons':
                    loadSeries(currentData.categoryId, currentData.categoryName);
                    break;
                case 'series':
                    loadSeriesCategories();
                    break;
            }
        }

        // Actualizar breadcrumb
        function updateBreadcrumb(items) {
            const breadcrumb = document.getElementById('breadcrumb');
            if (!breadcrumb) return;

            breadcrumb.innerHTML = items.map((item, index) => {
                const isLast = index === items.length - 1;
                return `
                    <span class="breadcrumb-item ${isLast ? '' : 'clickable'}" ${item.action ? `onclick="(${item.action.toString()})()"` : ''}>
                        ${item.text}
                    </span>
                    ${!isLast ? '<span class="breadcrumb-separator">></span>' : ''}
                `;
            }).join('');
        }

        // Cerrar modal
        document.getElementById('closeModal').addEventListener('click', () => {
            const modal = document.getElementById('playerModal');
            modal.classList.add('hidden');

            // Detener reproductor
            if (currentPlayer) {
                destroyPlayer();
            }
        });
    </script>
</body>
</html>
