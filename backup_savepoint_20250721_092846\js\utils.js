// Utilidades generales
const UTILS = {
    // Configuración de animaciones
    animations: {
        duration: 300,
        easing: 'ease-in-out'
    },
    
    // Configuración de cache
    cache: {
        duration: 5 * 60 * 1000, // 5 minutos
        maxSize: 100
    }
};

// Verificar autenticación en todas las páginas
function checkAuthentication() {
    const token = localStorage.getItem('authToken');
    const currentPage = window.location.pathname;
    
    if (!token && !currentPage.includes('index.html') && currentPage !== '/') {
        window.location.href = 'index.html';
        return false;
    }
    
    return true;
}

// Navegación con animaciones
function navigateToPage(page, data = null) {
    // Guardar datos para la siguiente página si es necesario
    if (data) {
        sessionStorage.setItem('pageData', JSON.stringify(data));
    }
    
    // Animación de salida
    document.body.style.opacity = '0';
    document.body.style.transition = `opacity ${UTILS.animations.duration}ms ${UTILS.animations.easing}`;
    
    setTimeout(() => {
        window.location.href = page;
    }, UTILS.animations.duration);
}

// Obtener datos de la página anterior
function getPageData() {
    const data = sessionStorage.getItem('pageData');
    if (data) {
        sessionStorage.removeItem('pageData');
        return JSON.parse(data);
    }
    return null;
}

// Animación de entrada para páginas
function animatePageEntry() {
    document.body.style.opacity = '0';
    document.body.style.transition = `opacity ${UTILS.animations.duration}ms ${UTILS.animations.easing}`;
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 50);
}

// Formatear duración en formato legible
function formatDuration(seconds) {
    if (!seconds || seconds === 0) return '--:--';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

// Formatear fecha
function formatDate(timestamp) {
    if (!timestamp) return 'Fecha no disponible';
    
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Formatear tamaño de archivo
function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return 'N/A';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

// Debounce para optimizar búsquedas
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle para optimizar scroll
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Crear elemento con clases y atributos
function createElement(tag, classes = [], attributes = {}, content = '') {
    const element = document.createElement(tag);
    
    if (classes.length > 0) {
        element.classList.add(...classes);
    }
    
    Object.keys(attributes).forEach(key => {
        element.setAttribute(key, attributes[key]);
    });
    
    if (content) {
        element.innerHTML = content;
    }
    
    return element;
}

// Mostrar modal genérico
function showModal(title, content, actions = []) {
    // Remover modal existente
    const existingModal = document.querySelector('.generic-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Crear modal
    const modal = createElement('div', ['modal', 'generic-modal']);
    const modalContent = createElement('div', ['modal-content']);
    
    // Header
    const header = createElement('div', ['modal-header']);
    const titleElement = createElement('h3', [], {}, title);
    const closeBtn = createElement('button', ['close-btn'], {}, '×');
    
    closeBtn.addEventListener('click', () => hideModal(modal));
    
    header.appendChild(titleElement);
    header.appendChild(closeBtn);
    
    // Body
    const body = createElement('div', ['modal-body']);
    if (typeof content === 'string') {
        body.innerHTML = content;
    } else {
        body.appendChild(content);
    }
    
    // Footer con acciones
    const footer = createElement('div', ['modal-footer']);
    actions.forEach(action => {
        const btn = createElement('button', ['modal-btn', action.class || ''], {}, action.text);
        btn.addEventListener('click', () => {
            if (action.handler) action.handler();
            if (action.closeModal !== false) hideModal(modal);
        });
        footer.appendChild(btn);
    });
    
    // Ensamblar modal
    modalContent.appendChild(header);
    modalContent.appendChild(body);
    if (actions.length > 0) {
        modalContent.appendChild(footer);
    }
    modal.appendChild(modalContent);
    
    // Agregar al DOM
    document.body.appendChild(modal);
    
    // Animación de entrada
    setTimeout(() => modal.classList.add('show'), 10);
    
    return modal;
}

// Ocultar modal
function hideModal(modal) {
    modal.classList.remove('show');
    setTimeout(() => {
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }, 300);
}

// Mostrar confirmación
function showConfirmation(message, onConfirm, onCancel = null) {
    return showModal('Confirmación', message, [
        {
            text: 'Cancelar',
            class: 'btn-secondary',
            handler: onCancel
        },
        {
            text: 'Confirmar',
            class: 'btn-primary',
            handler: onConfirm
        }
    ]);
}

// Mostrar loading overlay
function showLoading(message = 'Cargando...') {
    const loading = createElement('div', ['loading-overlay'], {}, `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-message">${message}</div>
        </div>
    `);
    
    document.body.appendChild(loading);
    return loading;
}

// Ocultar loading overlay
function hideLoading() {
    const loading = document.querySelector('.loading-overlay');
    if (loading) {
        loading.remove();
    }
}

// Validar URL
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// Obtener parámetros de URL
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    const result = {};
    for (const [key, value] of params) {
        result[key] = value;
    }
    return result;
}

// Copiar al portapapeles
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showNotification('Copiado al portapapeles', 'success');
        return true;
    } catch (err) {
        console.error('Error copying to clipboard:', err);
        showNotification('Error al copiar', 'error');
        return false;
    }
}

// Detectar dispositivo móvil
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// Detectar soporte de características
function getDeviceCapabilities() {
    return {
        fullscreen: document.fullscreenEnabled,
        pictureInPicture: document.pictureInPictureEnabled,
        clipboard: navigator.clipboard !== undefined,
        touch: 'ontouchstart' in window,
        mobile: isMobile()
    };
}

// Inicializar utilidades cuando se carga la página
document.addEventListener('DOMContentLoaded', () => {
    // Verificar autenticación
    checkAuthentication();
    
    // Animación de entrada
    animatePageEntry();
    
    // Configurar eventos globales
    setupGlobalEvents();
});

// Configurar eventos globales
function setupGlobalEvents() {
    // Prevenir zoom en dispositivos móviles
    if (isMobile()) {
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });
    }
    
    // Manejar errores globales
    window.addEventListener('error', (e) => {
        console.error('Global error:', e.error);
    });
    
    // Manejar promesas rechazadas
    window.addEventListener('unhandledrejection', (e) => {
        console.error('Unhandled promise rejection:', e.reason);
    });
}

// CSS adicional para utilidades
const utilsCSS = `
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.modal.show {
    opacity: 1;
    pointer-events: all;
}

.modal-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
`;

// Agregar CSS al documento
const utilsStyle = document.createElement('style');
utilsStyle.textContent = utilsCSS;
document.head.appendChild(utilsStyle);
