📄 RGSMDIA_Player - Instrucciones Técnicas y Funcionales.txt
✅ 1. PROBLEMÁTICA INICIAL: HTTPS Forzado en Chrome
Chrome bloquea contenido no seguro (HTTP) cuando se carga en páginas HTTPS.

Solución recomendada:

Cargar todo el proyecto en HTTP directamente, evitando HTTPS en el hosting.

O usar un subdominio dedicado sin SSL (por ejemplo, http://panel.rogsworld.uk:2052) con ofuscación.

Alternativa técnica (más compleja): crear una pasarela PHP proxy que obtenga el contenido HTTP y lo sirva en HTTPS.

🔐 2. LOGIN AUTOMÁTICO CON ANIMACIÓN
Pantalla inicial:

Recuadro centrado con login (usuario, contraseña)

Checkbox: “Recordar mis datos”

Animación estilo "Call of Duty loading bar"

Después del login exitoso:

Almacenar token de sesión, datos en localStorage

Redirigir a menú principal

📺 3. PANTALLA PRINCIPAL CON 4 TARJETAS
Canales en vivo

Películas

Series

Ajustes e información

Diseño moderno, responsivo (compatible con móvil/PC), con transiciones suaves.

📡 4. PÁGINA DE TV EN VIVO
Estructura:

Barra lateral izquierda (scroll vertical animado) → Categorías

Panel central: canales por categoría

Espacio superior: reproductor de video

Debajo: EPG (guía electrónica)

Controles del reproductor:

Pantalla completa

PiP (Picture-in-Picture)

Teclas de volumen

Integración de EPG desde API XUI/XC

🎬 5. PÁGINA DE PELÍCULAS
Barra lateral: categorías de películas

Panel de contenido: tarjetas de películas

Al hacer clic:

Abrir reproductor

Cargar película por stream_id

Ir hacia atrás:

Volver a categoría / menú

📺 6. PÁGINA DE SERIES
Igual estructura que películas, pero:

Al hacer clic en una serie → Ver temporadas

Luego episodios

Reproducir episodio seleccionado

⚙️ 7. PÁGINA DE AJUSTES
Opciones disponibles:

Elegir tipo de reproductor (por ejemplo: video.js, hls.js, clappr)

Elegir tipo de animación

Mejorar velocidad (ajustes prefetch/cache)

Forzar audio: español latino

Forzar subtítulos si disponibles

🔌 8. CONEXIÓN CON API XTREAM CODES / XUI.ONE
Peticiones API a:

bash
Copiar
Editar
http://rogsworld.uk:2052/player_api.php?username=XXX&password=XXX
Obtener datos como:

Lista de categorías

Streams: canales, películas, series

Info EPG

Fecha de expiración y credenciales

Importante: ofuscar la URL en el código JavaScript (base64, desencriptación en tiempo real)

🖥️ 9. TECNOLOGÍAS RECOMENDADAS
Como es para hosting compartido (Hostinger), usar tecnologías ampliamente compatibles:

Frontend: HTML5, CSS3 (Tailwind o Bootstrap opcional), JavaScript (sin frameworks pesados)

Backend: PHP puro (para proxy y manejo de sesiones)

Player: hls.js o video.js (ligero, sin dependencias grandes)

📦 10. FUNCIONALIDADES ADICIONALES
Guardado de sesión y login automático

Diseño responsivo (usando media queries y contenedores fluidos)

Animaciones suaves (CSS y JS)

Control por teclado: volumen, retroceso, pantalla completa

Modularidad: que cada sección (TV, Pelis, Series) sea una vista independiente

🧩 ESTRUCTURA DE ARCHIVOS SUGERIDA
pgsql
Copiar
Editar
/RGSMDIA_Player/
│
├── index.html           → Login
├── home.html            → Pantalla principal (4 tarjetas)
├── tv.html              → TV en vivo
├── movies.html          → Películas
├── series.html          → Series
├── settings.html        → Ajustes
│
├── js/
│   ├── auth.js          → Login, sesión
│   ├── api.js           → Llamadas a XUI/XC
│   ├── player.js        → Configuración de reproductor
│   └── utils.js         → Ofuscación, navegación
│
├── css/
│   └── style.css        → Estilos base + animaciones
│
├── assets/              → Íconos, logos, fuentes, etc.
└── php/
    └── proxy.php        → (opcional) para pasar streams HTTP si se necesita HTTPS