<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Connection - IPTV API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test de Conexión IPTV API</h1>
        <p>Esta página te ayuda a probar la conexión con el servidor IPTV sin problemas de SSL.</p>
        
        <div class="input-group">
            <label for="testUsername">Usuario:</label>
            <input type="text" id="testUsername" value="Casa122">
        </div>
        
        <div class="input-group">
            <label for="testPassword">Contraseña:</label>
            <input type="password" id="testPassword" value="Panama21">
        </div>
        
        <button class="test-button" onclick="testPlayerAPI()">Test Player API</button>
        <button class="test-button" onclick="testM3UEndpoint()">Test M3U Endpoint</button>
        <button class="test-button" onclick="testBothMethods()">Test Ambos Métodos</button>
        
        <div id="results"></div>
    </div>

    <script>
        function showResult(message, isSuccess = false) {
            const results = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            results.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testPlayerAPI() {
            clearResults();
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            
            const url = `http://rogsworld.uk:2052/player_api.php?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;
            
            showResult(`Testing Player API: ${url}`);
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    },
                    mode: 'cors'
                });
                
                showResult(`Response Status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(`Response Data: ${JSON.stringify(data, null, 2)}`, true);
                } else {
                    showResult(`Error: HTTP ${response.status}`);
                }
            } catch (error) {
                showResult(`Fetch Error: ${error.message}`);
                console.error('Player API Error:', error);
            }
        }

        async function testM3UEndpoint() {
            clearResults();
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            
            const url = `http://rogsworld.uk:2052/get.php?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&type=m3u_plus&output=mpegts`;
            
            showResult(`Testing M3U Endpoint: ${url}`);
            
            try {
                const response = await fetch(url, {
                    method: 'HEAD', // Solo verificar headers
                    mode: 'cors'
                });
                
                showResult(`Response Status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    showResult(`M3U Endpoint is accessible!`, true);
                    
                    // Intentar obtener una muestra del contenido
                    const contentResponse = await fetch(url, {
                        method: 'GET',
                        mode: 'cors'
                    });
                    
                    if (contentResponse.ok) {
                        const content = await contentResponse.text();
                        const preview = content.substring(0, 500) + (content.length > 500 ? '...' : '');
                        showResult(`M3U Content Preview:\n${preview}`, true);
                    }
                } else {
                    showResult(`Error: HTTP ${response.status}`);
                }
            } catch (error) {
                showResult(`Fetch Error: ${error.message}`);
                console.error('M3U Endpoint Error:', error);
            }
        }

        async function testBothMethods() {
            showResult("=== Testing Player API ===");
            await testPlayerAPI();
            
            setTimeout(async () => {
                showResult("\n=== Testing M3U Endpoint ===");
                await testM3UEndpoint();
            }, 1000);
        }

        // Auto-load test on page load
        window.addEventListener('DOMContentLoaded', () => {
            showResult("Página cargada. Haz clic en los botones para probar la conexión.");
        });
    </script>
</body>
</html>
