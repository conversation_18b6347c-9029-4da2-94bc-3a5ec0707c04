/* Reset y base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #fff;
    min-height: 100vh;
}

/* Login Styles */
.login-body {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-box {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo h1 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2rem;
    background: linear-gradient(45deg, #fff, #a8d8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.input-group {
    margin-bottom: 20px;
}

.input-group input {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.input-group input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.input-group input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.checkbox-group input {
    margin-right: 10px;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

/* Loading Bar - Call of Duty Style */
.loading-bar {
    margin-top: 20px;
    text-align: center;
}

.loading-progress {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 10px;
}

.loading-progress::after {
    content: '';
    display: block;
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    animation: loadingProgress 3s ease-in-out forwards;
}

@keyframes loadingProgress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.loading-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.back-btn, .logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-btn:hover, .logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Home Cards */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.card h3 {
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.card p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

/* TV Layout */
.tv-layout, .movies-layout, .series-layout {
    display: flex;
    min-height: calc(100vh - 70px);
}

.sidebar {
    width: 250px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    overflow-y: auto;
}

.sidebar h3 {
    margin-bottom: 20px;
    color: #fff;
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.category-item {
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.category-item:hover, .category-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: #4CAF50;
}

.content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* Player Section */
.player-section {
    margin-bottom: 30px;
}

.video-player {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
}

.video-player video {
    width: 100%;
    height: auto;
    display: block;
}

.player-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
}

.player-controls button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.player-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
}

.volume-control input[type="range"] {
    width: 100px;
}

/* Channels/Movies Grid */
.channels-grid, .movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.channel-item, .movie-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.channel-item:hover, .movie-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.channel-logo, .movie-poster {
    width: 100%;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
}

.channel-name, .movie-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.channel-category, .movie-year {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

/* EPG Section */
.epg-section {
    margin-top: 30px;
}

.epg-container {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
}

.epg-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.epg-time {
    font-weight: bold;
    color: #4CAF50;
}

.epg-title {
    flex: 1;
    margin-left: 15px;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: rgba(30, 60, 114, 0.95);
    border-radius: 15px;
    width: 90%;
    max-width: 900px;
    max-height: 90%;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.close-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 20px;
}

/* Settings */
.settings-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.settings-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-section:last-of-type {
    border-bottom: none;
}

.settings-section h3 {
    margin-bottom: 20px;
    color: #4CAF50;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.setting-item label {
    flex: 1;
    margin-right: 20px;
}

.setting-item select, .setting-item input[type="checkbox"] {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 8px 12px;
    border-radius: 6px;
}

.setting-item select {
    min-width: 150px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 10px 0;
}

.settings-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.save-btn, .reset-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.save-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.reset-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.save-btn:hover, .reset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Breadcrumb */
.breadcrumb {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-item {
    display: inline;
    cursor: pointer;
}

.breadcrumb-item:hover {
    color: #4CAF50;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: rgba(255, 255, 255, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
    .tv-layout, .movies-layout, .series-layout {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: 200px;
    }

    .cards-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .login-box {
        padding: 30px 20px;
    }

    .header-content {
        padding: 0 15px;
    }

    .main-content, .settings-content {
        padding: 20px 15px;
    }

    .channels-grid, .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .settings-actions {
        flex-direction: column;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .setting-item select {
        width: 100%;
    }
}
