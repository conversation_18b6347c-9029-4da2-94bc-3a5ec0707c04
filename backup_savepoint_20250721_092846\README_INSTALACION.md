# RGSMDIA Player - Guía de Instalación

## 🚀 Instalación Rápida

### Opción 1: Servidor Local con Python (Más Fácil)

1. **Ejecutar servidor automático**
   - Windows: Doble clic en `start_server.bat`
   - Linux/Mac: `chmod +x start_server.sh && ./start_server.sh`
   - Manual: `python -m http.server 8000`

2. **Acceder al player**
   - Abrir navegador en: http://localhost:8000
   - Usar credenciales: Casa122 / Panama21

### Opción 2: Servidor Local con XAMPP

1. **Descargar e instalar XAMPP/WAMP/MAMP**
   - Descargar desde: https://www.apachefriends.org/
   - Instalar y ejecutar Apache + PHP

2. **Copiar archivos del proyecto**
   ```
   Copiar toda la carpeta del proyecto a:
   - XAMPP: C:\xampp\htdocs\rgsmdia-player\
   - WAMP: C:\wamp64\www\rgsmdia-player\
   - MAMP: /Applications/MAMP/htdocs/rgsmdia-player/
   ```

3. **Acceder al proyecto**
   - Abrir navegador en: http://localhost/rgsmdia-player/
   - Usar credenciales: Casa122 / Panama21 o demo / demo

### Opción 3: Hosting Compartido Hostinger (Recomendado para producción)

1. **Subir archivos por FTP**
   - Conectar a Hostinger via FTP/File Manager
   - Subir todos los archivos a la carpeta public_html/

2. **Configurar permisos**
   ```
   Carpeta php/: 755
   Archivo proxy.php: 644
   Todos los archivos .html: 644
   Carpeta js/: 755
   Carpeta css/: 755
   ```

3. **Verificar PHP**
   - Hostinger tiene PHP habilitado por defecto
   - El proxy.php manejará automáticamente las requests HTTP

4. **Acceder desde tu dominio**
   - https://tudominio.com/
   - El sistema usará automáticamente el proxy PHP para evitar Mixed Content

## ⚙️ Configuración

### Credenciales de Servidor

Editar en `js/auth.js` y `js/api.js`:

```javascript
// Cambiar la URL del servidor
const AUTH_CONFIG = {
    apiBase: 'http://TU-SERVIDOR:PUERTO',
    // ...
};
```

### Credenciales de Usuario

El sistema acepta estas credenciales por defecto:
- **Usuario:** Casa122, **Contraseña:** Panama21
- **Usuario:** demo, **Contraseña:** demo

Para agregar más usuarios, editar la función `authenticateUser()` en `js/auth.js`.

## 🔧 Solución de Problemas

### Error SSL/HTTPS (Solucionado automáticamente)

El sistema ahora usa proxy PHP automáticamente:

1. **Proxy PHP integrado:**
   - Todas las requests HTTP se manejan via `php/proxy.php`
   - Funciona automáticamente en hosting HTTPS
   - No requiere configuración adicional

2. **Si el proxy no funciona:**
   - Verificar que PHP esté habilitado en Hostinger
   - Verificar permisos del archivo proxy.php (644)
   - Revisar logs de error del hosting

### Error CORS

Si hay problemas de CORS:

1. **Configurar .htaccess:**
   ```apache
   Header always set Access-Control-Allow-Origin "*"
   Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
   Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
   ```

2. **Usar proxy PHP:**
   - El proxy maneja automáticamente CORS
   - Verificar que `php/proxy.php` sea accesible

### Streams no cargan

1. **Verificar URLs:**
   - Formato correcto: http://servidor:puerto/live/user/pass/id.ts
   - Verificar credenciales en localStorage

2. **Probar manualmente:**
   - Abrir URL de stream directamente en navegador
   - Verificar que el servidor IPTV esté funcionando

## 📱 Compatibilidad

### Navegadores Soportados
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Dispositivos
- PC/Mac (recomendado)
- Tablets
- Móviles (funcionalidad limitada)

### Reproductores
- Video.js (por defecto)
- HLS.js (para streams HLS)
- Clappr (alternativo)

## 🔐 Seguridad

### Recomendaciones

1. **Cambiar credenciales por defecto**
2. **Usar HTTPS cuando sea posible**
3. **Configurar firewall en servidor**
4. **Limitar acceso por IP si es necesario**

### Ofuscación

Las URLs están parcialmente ofuscadas en el código. Para mayor seguridad:

1. **Usar base64:**
   ```javascript
   apiBase: atob('aHR0cDovL3R1c2Vydmlkb3I6cHVlcnRv')
   ```

2. **Implementar autenticación JWT**
3. **Usar proxy con autenticación**

## 📞 Soporte

Si tienes problemas:

1. **Verificar consola del navegador** (F12)
2. **Revisar logs del servidor web**
3. **Probar con credenciales demo/demo**
4. **Verificar conectividad al servidor IPTV**

## 🎯 Características Principales

- ✅ Login con animación Call of Duty
- ✅ TV en vivo con EPG
- ✅ Películas por categorías  
- ✅ Series con temporadas/episodios
- ✅ Configuración personalizable
- ✅ Diseño responsivo
- ✅ Controles de teclado
- ✅ Picture-in-Picture
- ✅ Pantalla completa

## 🔄 Actualizaciones

Para actualizar:

1. **Hacer backup de configuración**
2. **Reemplazar archivos (excepto configuración)**
3. **Limpiar cache del navegador**
4. **Probar funcionalidad**
