<?php
/**
 * Proxy PHP para manejar streams HTTP en entornos HTTPS
 * Este archivo permite servir contenido HTTP a través de HTTPS
 */

// Configuración de seguridad
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuración
$config = [
    'allowed_domains' => [
        'rogsworld.uk',
        'localhost',
        '127.0.0.1'
    ],
    'max_file_size' => 100 * 1024 * 1024, // 100MB
    'timeout' => 30,
    'user_agent' => 'RGSMDIA Player/1.0'
];

/**
 * Validar URL de origen
 */
function validateUrl($url, $allowedDomains) {
    $parsedUrl = parse_url($url);
    
    if (!$parsedUrl || !isset($parsedUrl['host'])) {
        return false;
    }
    
    foreach ($allowedDomains as $domain) {
        if (strpos($parsedUrl['host'], $domain) !== false) {
            return true;
        }
    }
    
    return false;
}

/**
 * Obtener tipo MIME basado en extensión
 */
function getMimeType($url) {
    $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
    
    $mimeTypes = [
        'ts' => 'video/mp2t',
        'm3u8' => 'application/vnd.apple.mpegurl',
        'mp4' => 'video/mp4',
        'mkv' => 'video/x-matroska',
        'avi' => 'video/x-msvideo',
        'mov' => 'video/quicktime',
        'wmv' => 'video/x-ms-wmv',
        'flv' => 'video/x-flv',
        'webm' => 'video/webm'
    ];
    
    return isset($mimeTypes[$extension]) ? $mimeTypes[$extension] : 'application/octet-stream';
}

/**
 * Proxy principal
 */
function proxyStream($url, $config) {
    // Validar URL
    if (!validateUrl($url, $config['allowed_domains'])) {
        http_response_code(403);
        echo json_encode(['error' => 'URL no permitida']);
        return;
    }
    
    // Configurar contexto de stream
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => $config['timeout'],
            'user_agent' => $config['user_agent'],
            'follow_location' => true,
            'max_redirects' => 5
        ]
    ]);
    
    // Obtener headers del archivo remoto
    $headers = get_headers($url, 1, $context);
    
    if (!$headers || strpos($headers[0], '200') === false) {
        http_response_code(404);
        echo json_encode(['error' => 'Archivo no encontrado']);
        return;
    }
    
    // Configurar headers de respuesta
    $contentType = getMimeType($url);
    $contentLength = isset($headers['Content-Length']) ? $headers['Content-Length'] : null;
    
    header("Content-Type: $contentType");
    
    if ($contentLength) {
        header("Content-Length: $contentLength");
        
        // Verificar tamaño máximo
        if ($contentLength > $config['max_file_size']) {
            http_response_code(413);
            echo json_encode(['error' => 'Archivo demasiado grande']);
            return;
        }
    }
    
    // Headers para streaming
    header('Accept-Ranges: bytes');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Manejar range requests para video streaming
    if (isset($_SERVER['HTTP_RANGE']) && $contentLength) {
        handleRangeRequest($url, $contentLength, $context);
        return;
    }
    
    // Stream completo
    $stream = fopen($url, 'rb', false, $context);
    
    if (!$stream) {
        http_response_code(500);
        echo json_encode(['error' => 'Error al abrir stream']);
        return;
    }
    
    // Enviar contenido en chunks
    while (!feof($stream)) {
        echo fread($stream, 8192);
        flush();
        
        // Verificar si el cliente se desconectó
        if (connection_aborted()) {
            break;
        }
    }
    
    fclose($stream);
}

/**
 * Manejar requests de rango para streaming de video
 */
function handleRangeRequest($url, $contentLength, $context) {
    $range = $_SERVER['HTTP_RANGE'];
    
    // Parsear range header
    if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
        $start = intval($matches[1]);
        $end = $matches[2] ? intval($matches[2]) : $contentLength - 1;
        
        // Validar rango
        if ($start >= $contentLength || $end >= $contentLength || $start > $end) {
            http_response_code(416);
            header("Content-Range: bytes */$contentLength");
            return;
        }
        
        $length = $end - $start + 1;
        
        // Headers para partial content
        http_response_code(206);
        header("Content-Range: bytes $start-$end/$contentLength");
        header("Content-Length: $length");
        
        // Abrir stream con offset
        $stream = fopen($url, 'rb', false, $context);
        
        if (!$stream) {
            http_response_code(500);
            return;
        }
        
        fseek($stream, $start);
        
        $remaining = $length;
        while ($remaining > 0 && !feof($stream)) {
            $chunkSize = min(8192, $remaining);
            echo fread($stream, $chunkSize);
            $remaining -= $chunkSize;
            flush();
            
            if (connection_aborted()) {
                break;
            }
        }
        
        fclose($stream);
    }
}

/**
 * Endpoint principal
 */
try {
    // Obtener URL del parámetro
    $url = isset($_GET['url']) ? $_GET['url'] : '';
    
    if (empty($url)) {
        http_response_code(400);
        echo json_encode(['error' => 'URL requerida']);
        exit();
    }
    
    // Decodificar URL si está en base64
    if (isset($_GET['encoded']) && $_GET['encoded'] === 'true') {
        $url = base64_decode($url);
    }
    
    // Validar formato de URL
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        http_response_code(400);
        echo json_encode(['error' => 'URL inválida']);
        exit();
    }
    
    // Procesar proxy
    proxyStream($url, $config);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error interno del servidor: ' . $e->getMessage()]);
}
?>
