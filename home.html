<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RGSMDIA Player - Inicio</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="home-body">
    <header class="header">
        <div class="header-content">
            <h1>RGSMDIA Player</h1>
            <button id="logoutBtn" class="logout-btn">Cerrar Sesión</button>
        </div>
    </header>
    
    <main class="main-content">
        <div class="cards-container">
            <div class="card" data-page="tv">
                <div class="card-icon">📺</div>
                <h3>Canales en Vivo</h3>
                <p>Disfruta de televisión en tiempo real</p>
            </div>
            
            <div class="card" data-page="movies">
                <div class="card-icon">🎬</div>
                <h3>Películas</h3>
                <p>Explora nuestra colección de películas</p>
            </div>
            
            <div class="card" data-page="series">
                <div class="card-icon">📺</div>
                <h3>Series</h3>
                <p>Temporadas completas de tus series favoritas</p>
            </div>
            
            <div class="card" data-page="settings">
                <div class="card-icon">⚙️</div>
                <h3>Ajustes</h3>
                <p>Configura tu experiencia de reproducción</p>
            </div>
        </div>
    </main>
    
    <script src="js/utils.js"></script>
    <script>
        // Verificar autenticación
        if (!localStorage.getItem('authToken')) {
            window.location.href = 'index.html';
        }
        
        // Navegación entre páginas
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', () => {
                const page = card.dataset.page;
                window.location.href = `${page}.html`;
            });
        });
        
        // Logout
        document.getElementById('logoutBtn').addEventListener('click', () => {
            localStorage.clear();
            window.location.href = 'index.html';
        });
    </script>
</body>
</html>
