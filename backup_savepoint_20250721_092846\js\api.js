// Configuración de API
const API_CONFIG = {
    // Usar proxy PHP para hosting HTTPS
    proxyUrl: 'php/proxy.php',
    baseUrl: 'http://rogsworld.uk:2052',
    endpoints: {
        playerApi: '/player_api.php',
        getLiveCategories: '?action=get_live_categories',
        getLiveStreams: '?action=get_live_streams&category_id={categoryId}',
        getVodCategories: '?action=get_vod_categories',
        getVodStreams: '?action=get_vod_streams&category_id={categoryId}',
        getSeriesCategories: '?action=get_series_categories',
        getSeries: '?action=get_series&category_id={categoryId}',
        getSeriesInfo: '?action=get_series_info&series_id={seriesId}',
        getEpg: '?action=get_simple_data_table&stream_id={streamId}',
        getM3u: '/get.php?type=m3u_plus&output=mpegts'
    }
};

// Cache para optimizar rendimiento
const apiCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

// Obtener credenciales del usuario
function getCredentials() {
    const userData = JSON.parse(localStorage.getItem('userData') || '{}');
    return {
        username: userData.username || '',
        password: userData.password || ''
    };
}

// Construir URL con credenciales via proxy
function buildApiUrl(endpoint) {
    const credentials = getCredentials();
    const baseParams = `username=${encodeURIComponent(credentials.username)}&password=${encodeURIComponent(credentials.password)}`;
    const targetUrl = `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.playerApi}${endpoint}&${baseParams}`;

    return `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(targetUrl)}`;
}

// Realizar petición a la API con cache
async function apiRequest(endpoint, cacheKey = null) {
    // Verificar cache
    if (cacheKey && apiCache.has(cacheKey)) {
        const cached = apiCache.get(cacheKey);
        if (Date.now() - cached.timestamp < CACHE_DURATION) {
            return cached.data;
        }
    }

    try {
        const url = buildApiUrl(endpoint);
        console.log('API Request via proxy:', url);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        // Guardar en cache
        if (cacheKey) {
            apiCache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });
        }

        return data;
    } catch (error) {
        console.error('API Request error:', error);
        throw error;
    }
}

// Obtener categorías de TV en vivo
async function getLiveCategories() {
    try {
        const data = await apiRequest(API_CONFIG.endpoints.getLiveCategories, 'live_categories');
        console.log('Live categories data:', data);

        if (data && Array.isArray(data) && data.length > 0) {
            return data;
        }

        // Si no hay datos, intentar parsear M3U
        return await parseM3uForCategories('live');
    } catch (error) {
        console.error('Error getting live categories:', error);
        return await parseM3uForCategories('live');
    }
}

// Obtener canales por categoría
async function getLiveStreams(categoryId) {
    try {
        const endpoint = API_CONFIG.endpoints.getLiveStreams.replace('{categoryId}', categoryId);
        const data = await apiRequest(endpoint, `live_streams_${categoryId}`);
        console.log('Live streams data:', data);

        if (data && Array.isArray(data) && data.length > 0) {
            return data;
        }

        // Si no hay datos, intentar parsear M3U
        return await parseM3uForStreams('live', categoryId);
    } catch (error) {
        console.error('Error getting live streams:', error);
        return await parseM3uForStreams('live', categoryId);
    }
}

// Obtener categorías de películas
async function getVodCategories() {
    try {
        const data = await apiRequest(API_CONFIG.endpoints.getVodCategories, 'vod_categories');
        console.log('VOD categories data:', data);

        if (data && Array.isArray(data) && data.length > 0) {
            return data;
        }

        return await parseM3uForCategories('movie');
    } catch (error) {
        console.error('Error getting VOD categories:', error);
        return await parseM3uForCategories('movie');
    }
}

// Obtener películas por categoría
async function getVodStreams(categoryId) {
    try {
        const endpoint = API_CONFIG.endpoints.getVodStreams.replace('{categoryId}', categoryId);
        const data = await apiRequest(endpoint, `vod_streams_${categoryId}`);
        return data || [];
    } catch (error) {
        console.error('Error getting VOD streams:', error);
        return [];
    }
}

// Obtener categorías de series
async function getSeriesCategories() {
    try {
        const data = await apiRequest(API_CONFIG.endpoints.getSeriesCategories, 'series_categories');
        return data || [];
    } catch (error) {
        console.error('Error getting series categories:', error);
        return [];
    }
}

// Obtener series por categoría
async function getSeries(categoryId) {
    try {
        const endpoint = API_CONFIG.endpoints.getSeries.replace('{categoryId}', categoryId);
        const data = await apiRequest(endpoint, `series_${categoryId}`);
        return data || [];
    } catch (error) {
        console.error('Error getting series:', error);
        return [];
    }
}

// Obtener información detallada de una serie
async function getSeriesInfo(seriesId) {
    try {
        const endpoint = API_CONFIG.endpoints.getSeriesInfo.replace('{seriesId}', seriesId);
        const data = await apiRequest(endpoint, `series_info_${seriesId}`);
        return data || {};
    } catch (error) {
        console.error('Error getting series info:', error);
        return {};
    }
}

// Obtener EPG para un canal
async function getEpg(streamId) {
    try {
        const endpoint = API_CONFIG.endpoints.getEpg.replace('{streamId}', streamId);
        const data = await apiRequest(endpoint, `epg_${streamId}`);
        return data?.epg_listings || [];
    } catch (error) {
        console.error('Error getting EPG:', error);
        return [];
    }
}

// Construir URLs de streams - usar URL real del M3U si está disponible
function getStreamUrl(streamId, extension = 'ts') {
    // Buscar URL real en cache de streams
    const cachedStream = findCachedStream(streamId);
    if (cachedStream && cachedStream.stream_url) {
        return `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(cachedStream.stream_url)}`;
    }

    // Fallback a formato estándar
    const credentials = getCredentials();
    const streamUrl = `${API_CONFIG.baseUrl}/live/${credentials.username}/${credentials.password}/${streamId}.${extension}`;
    return `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(streamUrl)}`;
}

function getVodUrl(streamId, extension = 'mp4') {
    const cachedStream = findCachedStream(streamId);
    if (cachedStream && cachedStream.stream_url) {
        return `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(cachedStream.stream_url)}`;
    }

    const credentials = getCredentials();
    const streamUrl = `${API_CONFIG.baseUrl}/movie/${credentials.username}/${credentials.password}/${streamId}.${extension}`;
    return `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(streamUrl)}`;
}

function getSeriesUrl(streamId, extension = 'mp4') {
    const cachedStream = findCachedStream(streamId);
    if (cachedStream && cachedStream.stream_url) {
        return `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(cachedStream.stream_url)}`;
    }

    const credentials = getCredentials();
    const streamUrl = `${API_CONFIG.baseUrl}/series/${credentials.username}/${credentials.password}/${streamId}.${extension}`;
    return `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(streamUrl)}`;
}

// Buscar stream en cache
function findCachedStream(streamId) {
    for (const [key, cached] of apiCache.entries()) {
        if (key.includes('streams') && cached.data && Array.isArray(cached.data)) {
            const stream = cached.data.find(s => s.stream_id === streamId);
            if (stream) return stream;
        }
    }
    return null;
}

// Limpiar cache
function clearApiCache() {
    apiCache.clear();
}

// Verificar estado de la cuenta
async function checkAccountStatus() {
    try {
        const userData = JSON.parse(localStorage.getItem('userData') || '{}');
        if (!userData.userInfo) return false;
        
        const expiration = userData.userInfo.exp_date;
        if (expiration && expiration !== '0') {
            const expDate = new Date(expiration * 1000);
            const now = new Date();
            
            if (now > expDate) {
                return false; // Cuenta expirada
            }
        }
        
        return userData.userInfo.status === 'Active';
    } catch (error) {
        console.error('Error checking account status:', error);
        return false;
    }
}

// Funciones de utilidad para manejo de errores
function handleApiError(error, context = '') {
    console.error(`API Error ${context}:`, error);
    
    // Mostrar mensaje de error al usuario
    const errorMessage = error.message || 'Error de conexión con el servidor';
    showNotification(errorMessage, 'error');
}

// Mostrar notificación
function showNotification(message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? 'rgba(244, 67, 54, 0.9)' : 'rgba(76, 175, 80, 0.9)'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Remover después de 5 segundos
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// CSS para animaciones de notificación
const notificationCSS = `
@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
`;

// Agregar CSS al documento
const notificationStyle = document.createElement('style');
notificationStyle.textContent = notificationCSS;
document.head.appendChild(notificationStyle);

// Función para obtener la playlist M3U via proxy
async function getM3uPlaylist() {
    try {
        const credentials = getCredentials();
        const m3uTargetUrl = `${API_CONFIG.baseUrl}/get.php?username=${encodeURIComponent(credentials.username)}&password=${encodeURIComponent(credentials.password)}&type=m3u_plus&output=mpegts`;
        const m3uProxyUrl = `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(m3uTargetUrl)}`;

        console.log('M3U Playlist via proxy:', m3uProxyUrl);

        const response = await fetch(m3uProxyUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/x-mpegURL, text/plain, */*'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const playlistText = await response.text();
        return playlistText;
    } catch (error) {
        console.error('Error getting M3U playlist:', error);
        throw error;
    }
}

// Función para validar las credenciales usando el endpoint de playlist via proxy
async function validateCredentials(username, password) {
    try {
        const testTargetUrl = `${API_CONFIG.baseUrl}/get.php?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&type=m3u_plus&output=mpegts`;
        const testProxyUrl = `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(testTargetUrl)}`;

        const response = await fetch(testProxyUrl, {
            method: 'GET'
        });

        if (response.ok) {
            const content = await response.text();
            return content && content.includes('#EXTM3U');
        }
        return false;
    } catch (error) {
        console.error('Error validating credentials:', error);
        return false;
    }
}

// Parser M3U para extraer categorías y canales reales
async function parseM3uForCategories(type = 'live') {
    try {
        const m3uContent = await getM3uPlaylist();
        const categories = new Map();

        if (!m3uContent || !m3uContent.includes('#EXTM3U')) {
            return getDefaultCategories(type);
        }

        const lines = m3uContent.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            if (line.startsWith('#EXTINF:')) {
                // Extraer información del canal
                const info = parseExtinf(line);

                if (info && info.group && shouldIncludeInType(info, type)) {
                    const categoryId = generateCategoryId(info.group);
                    categories.set(categoryId, {
                        category_id: categoryId,
                        category_name: info.group
                    });
                }
            }
        }

        const result = Array.from(categories.values());
        console.log(`Parsed ${result.length} categories for ${type}:`, result);

        return result.length > 0 ? result : getDefaultCategories(type);
    } catch (error) {
        console.error('Error parsing M3U for categories:', error);
        return getDefaultCategories(type);
    }
}

// Parser M3U para extraer streams por categoría
async function parseM3uForStreams(type = 'live', categoryId) {
    try {
        const m3uContent = await getM3uPlaylist();
        const streams = [];

        if (!m3uContent || !m3uContent.includes('#EXTM3U')) {
            return getDefaultStreams(type);
        }

        const lines = m3uContent.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            if (line.startsWith('#EXTINF:')) {
                const nextLine = lines[i + 1]?.trim();

                if (nextLine && nextLine.startsWith('http')) {
                    const info = parseExtinf(line);

                    if (info && shouldIncludeInType(info, type)) {
                        const streamCategoryId = generateCategoryId(info.group);

                        if (!categoryId || streamCategoryId === categoryId) {
                            streams.push({
                                stream_id: generateStreamId(nextLine),
                                name: info.title,
                                category_name: info.group,
                                stream_url: nextLine,
                                stream_icon: info.logo || '',
                                epg_channel_id: info.tvgId || ''
                            });
                        }
                    }
                }
            }
        }

        console.log(`Parsed ${streams.length} streams for ${type}, category ${categoryId}:`, streams);

        return streams.length > 0 ? streams : getDefaultStreams(type);
    } catch (error) {
        console.error('Error parsing M3U for streams:', error);
        return getDefaultStreams(type);
    }
}

// Parsear línea EXTINF
function parseExtinf(line) {
    try {
        // Formato: #EXTINF:duration,title
        // Con atributos: #EXTINF:duration tvg-id="id" tvg-logo="logo" group-title="group",title

        const match = line.match(/#EXTINF:([^,]+),(.+)$/);
        if (!match) return null;

        const attributes = match[1];
        const title = match[2].trim();

        // Extraer atributos
        const tvgId = extractAttribute(attributes, 'tvg-id');
        const logo = extractAttribute(attributes, 'tvg-logo');
        const group = extractAttribute(attributes, 'group-title');

        return {
            title: title,
            tvgId: tvgId,
            logo: logo,
            group: group || 'Sin categoría'
        };
    } catch (error) {
        console.error('Error parsing EXTINF:', error);
        return null;
    }
}

// Extraer atributo de la línea EXTINF
function extractAttribute(line, attribute) {
    const regex = new RegExp(`${attribute}="([^"]*)"`, 'i');
    const match = line.match(regex);
    return match ? match[1] : '';
}

// Determinar si incluir en tipo específico
function shouldIncludeInType(info, type) {
    const group = info.group.toLowerCase();

    switch (type) {
        case 'live':
            return !group.includes('movie') && !group.includes('serie') && !group.includes('pelicula');
        case 'movie':
            return group.includes('movie') || group.includes('pelicula') || group.includes('film');
        case 'series':
            return group.includes('serie') || group.includes('series') || group.includes('show');
        default:
            return true;
    }
}

// Generar ID de categoría
function generateCategoryId(categoryName) {
    return btoa(categoryName).replace(/[^a-zA-Z0-9]/g, '').substring(0, 10);
}

// Generar ID de stream
function generateStreamId(url) {
    return btoa(url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 15);
}

// Categorías por defecto
function getDefaultCategories(type) {
    switch (type) {
        case 'live':
            return [
                { category_id: '1', category_name: 'Entretenimiento' },
                { category_id: '2', category_name: 'Deportes' },
                { category_id: '3', category_name: 'Noticias' },
                { category_id: '4', category_name: 'Infantil' }
            ];
        case 'movie':
            return [
                { category_id: '10', category_name: 'Acción' },
                { category_id: '11', category_name: 'Comedia' },
                { category_id: '12', category_name: 'Drama' },
                { category_id: '13', category_name: 'Terror' }
            ];
        case 'series':
            return [
                { category_id: '20', category_name: 'Drama' },
                { category_id: '21', category_name: 'Comedia' },
                { category_id: '22', category_name: 'Acción' },
                { category_id: '23', category_name: 'Documentales' }
            ];
        default:
            return [];
    }
}

// Streams por defecto
function getDefaultStreams(type) {
    switch (type) {
        case 'live':
            return [
                { stream_id: '1001', name: 'Canal Demo 1', category_name: 'Entretenimiento' },
                { stream_id: '1002', name: 'Canal Demo 2', category_name: 'Deportes' },
                { stream_id: '1003', name: 'Canal Demo 3', category_name: 'Noticias' }
            ];
        case 'movie':
            return [
                { stream_id: '2001', name: 'Película Demo 1', category_name: 'Acción' },
                { stream_id: '2002', name: 'Película Demo 2', category_name: 'Comedia' }
            ];
        case 'series':
            return [
                { stream_id: '3001', name: 'Serie Demo 1', category_name: 'Drama' },
                { stream_id: '3002', name: 'Serie Demo 2', category_name: 'Comedia' }
            ];
        default:
            return [];
    }
}
