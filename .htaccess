# RGSMDIA Player - Configuración para Hostinger
# Configuración de headers para CORS y proxy

# Habilitar headers CORS
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, HEAD"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, Accept"

# Manejar preflight requests
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Configuración de tipos MIME para streaming
AddType video/mp2t .ts
AddType application/vnd.apple.mpegurl .m3u8
AddType video/mp4 .mp4
AddType application/x-mpegURL .m3u

# Configuración de cache para archivos estáticos
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
    Header append Cache-Control "public"
</FilesMatch>

# Configuración de seguridad básica
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# Prevenir acceso directo a archivos de configuración
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# Configuración para PHP (si es necesario)
php_value max_execution_time 300
php_value memory_limit 256M
php_value upload_max_filesize 100M
php_value post_max_size 100M

# Redirección de errores (opcional)
ErrorDocument 404 /index.html
ErrorDocument 403 /index.html
