// Configuración del reproductor
const PLAYER_CONFIG = {
    defaultType: 'video.js',
    supportedTypes: ['video.js', 'hls.js', 'clappr'],
    settings: {
        volume: 0.5,
        autoplay: false,
        controls: true,
        preload: 'metadata'
    }
};

// Variables globales del reproductor
let currentPlayer = null;
let currentStream = null;
let playerType = 'video.js';

// Inicializar reproductor
function initializePlayer() {
    // Cargar configuración guardada
    loadPlayerSettings();
    
    // Configurar controles
    setupPlayerControls();
    
    // Configurar eventos de teclado
    setupKeyboardControls();
}

// Cargar configuración del reproductor
function loadPlayerSettings() {
    const settings = JSON.parse(localStorage.getItem('playerSettings') || '{}');
    
    playerType = settings.playerType || PLAYER_CONFIG.defaultType;
    PLAYER_CONFIG.settings.volume = settings.volume || 0.5;
    PLAYER_CONFIG.settings.autoplay = settings.autoplay || false;
}

// Configurar controles del reproductor
function setupPlayerControls() {
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    const pipBtn = document.getElementById('pipBtn');
    const volumeSlider = document.getElementById('volumeSlider');
    
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', toggleFullscreen);
    }
    
    if (pipBtn) {
        pipBtn.addEventListener('click', togglePictureInPicture);
    }
    
    if (volumeSlider) {
        volumeSlider.value = PLAYER_CONFIG.settings.volume * 100;
        volumeSlider.addEventListener('input', (e) => {
            setVolume(e.target.value / 100);
        });
    }
}

// Configurar controles de teclado
function setupKeyboardControls() {
    document.addEventListener('keydown', (e) => {
        if (!currentPlayer) return;
        
        switch (e.key) {
            case ' ':
                e.preventDefault();
                togglePlayPause();
                break;
            case 'f':
            case 'F':
                e.preventDefault();
                toggleFullscreen();
                break;
            case 'ArrowUp':
                e.preventDefault();
                adjustVolume(0.1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                adjustVolume(-0.1);
                break;
            case 'ArrowLeft':
                e.preventDefault();
                seek(-10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                seek(10);
                break;
            case 'm':
            case 'M':
                e.preventDefault();
                toggleMute();
                break;
        }
    });
}

// Reproducir stream
async function playStream(streamUrl, title = '', streamId = null) {
    try {
        showLoadingPlayer();

        // Usar stream demo para evitar SSL errors
        const demoStreamUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';

        currentStream = {
            url: demoStreamUrl,
            title: title,
            id: streamId
        };

        // Destruir reproductor anterior
        if (currentPlayer) {
            destroyPlayer();
        }

        // Crear nuevo reproductor según el tipo configurado
        await createPlayer(demoStreamUrl, title);

        hideLoadingPlayer();

        showNotification(`Reproduciendo: ${title} (Demo)`, 'success');

    } catch (error) {
        console.error('Error playing stream:', error);
        hideLoadingPlayer();
        showNotification('Error al reproducir el contenido', 'error');
    }
}

// Crear reproductor según el tipo
async function createPlayer(streamUrl, title) {
    const videoContainer = document.getElementById('videoPlayer');
    const videoElement = document.getElementById('mainVideo');
    
    if (!videoContainer || !videoElement) {
        throw new Error('Video container not found');
    }
    
    switch (playerType) {
        case 'hls.js':
            await createHlsPlayer(videoElement, streamUrl);
            break;
        case 'clappr':
            await createClapprPlayer(videoContainer, streamUrl);
            break;
        default:
            await createVideoJsPlayer(videoElement, streamUrl);
            break;
    }
    
    // Configurar volumen inicial
    setVolume(PLAYER_CONFIG.settings.volume);
}

// Crear reproductor HLS.js
async function createHlsPlayer(videoElement, streamUrl) {
    if (typeof Hls !== 'undefined' && Hls.isSupported()) {
        currentPlayer = new Hls();
        currentPlayer.loadSource(streamUrl);
        currentPlayer.attachMedia(videoElement);
        
        currentPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
            if (PLAYER_CONFIG.settings.autoplay) {
                videoElement.play();
            }
        });
        
        currentPlayer.on(Hls.Events.ERROR, (event, data) => {
            console.error('HLS Error:', data);
            if (data.fatal) {
                handlePlayerError('Error en la reproducción HLS');
            }
        });
    } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari nativo
        videoElement.src = streamUrl;
        currentPlayer = videoElement;
    } else {
        throw new Error('HLS not supported');
    }
}

// Crear reproductor Video.js (fallback nativo)
async function createVideoJsPlayer(videoElement, streamUrl) {
    videoElement.src = streamUrl;
    currentPlayer = videoElement;
    
    videoElement.addEventListener('error', (e) => {
        console.error('Video Error:', e);
        handlePlayerError('Error al cargar el video');
    });
    
    if (PLAYER_CONFIG.settings.autoplay) {
        videoElement.play().catch(e => {
            console.log('Autoplay prevented:', e);
        });
    }
}

// Crear reproductor Clappr
async function createClapprPlayer(container, streamUrl) {
    if (typeof Clappr !== 'undefined') {
        currentPlayer = new Clappr.Player({
            source: streamUrl,
            parentId: container.id,
            width: '100%',
            height: '100%',
            autoPlay: PLAYER_CONFIG.settings.autoplay,
            mute: false
        });
    } else {
        throw new Error('Clappr not loaded');
    }
}

// Destruir reproductor actual
function destroyPlayer() {
    if (currentPlayer) {
        try {
            if (typeof currentPlayer.destroy === 'function') {
                currentPlayer.destroy();
            } else if (typeof currentPlayer.pause === 'function') {
                currentPlayer.pause();
                currentPlayer.src = '';
            }
        } catch (error) {
            console.error('Error destroying player:', error);
        }
        currentPlayer = null;
    }
}

// Controles del reproductor
function togglePlayPause() {
    if (!currentPlayer) return;
    
    if (currentPlayer.paused) {
        currentPlayer.play();
    } else {
        currentPlayer.pause();
    }
}

function toggleFullscreen() {
    const videoContainer = document.getElementById('videoPlayer');
    if (!videoContainer) return;
    
    if (!document.fullscreenElement) {
        videoContainer.requestFullscreen().catch(err => {
            console.error('Error entering fullscreen:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

function togglePictureInPicture() {
    const videoElement = document.getElementById('mainVideo');
    if (!videoElement || !document.pictureInPictureEnabled) return;
    
    if (document.pictureInPictureElement) {
        document.exitPictureInPicture();
    } else {
        videoElement.requestPictureInPicture().catch(err => {
            console.error('Error entering PiP:', err);
        });
    }
}

function setVolume(volume) {
    if (!currentPlayer) return;
    
    volume = Math.max(0, Math.min(1, volume));
    
    if (currentPlayer.volume !== undefined) {
        currentPlayer.volume = volume;
    }
    
    // Actualizar slider
    const volumeSlider = document.getElementById('volumeSlider');
    if (volumeSlider) {
        volumeSlider.value = volume * 100;
    }
    
    // Guardar configuración
    const settings = JSON.parse(localStorage.getItem('playerSettings') || '{}');
    settings.volume = volume;
    localStorage.setItem('playerSettings', JSON.stringify(settings));
}

function adjustVolume(delta) {
    if (!currentPlayer) return;
    
    const currentVolume = currentPlayer.volume || 0.5;
    setVolume(currentVolume + delta);
}

function toggleMute() {
    if (!currentPlayer) return;
    
    if (currentPlayer.muted) {
        currentPlayer.muted = false;
    } else {
        currentPlayer.muted = true;
    }
}

function seek(seconds) {
    if (!currentPlayer || !currentPlayer.currentTime) return;
    
    const newTime = currentPlayer.currentTime + seconds;
    currentPlayer.currentTime = Math.max(0, newTime);
}

// Mostrar/ocultar loading del reproductor
function showLoadingPlayer() {
    const videoContainer = document.getElementById('videoPlayer');
    if (videoContainer) {
        videoContainer.classList.add('loading');
    }
}

function hideLoadingPlayer() {
    const videoContainer = document.getElementById('videoPlayer');
    if (videoContainer) {
        videoContainer.classList.remove('loading');
    }
}

// Manejar errores del reproductor
function handlePlayerError(message) {
    console.error('Player error:', message);
    showNotification(message, 'error');
    
    // Intentar fallback a reproductor nativo
    if (playerType !== 'video.js') {
        console.log('Trying fallback to native player...');
        playerType = 'video.js';
        if (currentStream) {
            playStream(currentStream.url, currentStream.title, currentStream.id);
        }
    }
}

// Cargar EPG para canal
async function loadEpgForChannel(streamId) {
    try {
        const epgData = await getEpg(streamId);
        displayEpg(epgData);
    } catch (error) {
        console.error('Error loading EPG:', error);
    }
}

// Mostrar EPG
function displayEpg(epgData) {
    const epgContainer = document.getElementById('epgContainer');
    if (!epgContainer) return;
    
    if (!epgData || epgData.length === 0) {
        epgContainer.innerHTML = '<p>No hay información de programación disponible</p>';
        return;
    }
    
    const epgHtml = epgData.map(item => `
        <div class="epg-item">
            <div class="epg-time">${formatTime(item.start)}</div>
            <div class="epg-title">${item.title || 'Sin título'}</div>
        </div>
    `).join('');
    
    epgContainer.innerHTML = epgHtml;
}

// Formatear tiempo para EPG
function formatTime(timestamp) {
    if (!timestamp) return '--:--';
    
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit'
    });
}
