# RGSMDIA Player - Guía de Instalación

## 🚀 Instalación Rápida

### Opción 1: Servidor Local (Recomendado para desarrollo)

1. **<PERSON><PERSON>gar e instalar XAMPP/WAMP/MAMP**
   - <PERSON><PERSON><PERSON> desde: https://www.apachefriends.org/
   - Instalar y ejecutar Apache + PHP

2. **Copiar archivos del proyecto**
   ```
   Copiar toda la carpeta del proyecto a:
   - XAMPP: C:\xampp\htdocs\rgsmdia-player\
   - WAMP: C:\wamp64\www\rgsmdia-player\
   - MAMP: /Applications/MAMP/htdocs/rgsmdia-player/
   ```

3. **Acceder al proyecto**
   - Abrir navegador en: http://localhost/rgsmdia-player/
   - Usar credenciales: Casa122 / Panama21 o demo / demo

### Opción 2: Hosting Compartido (Hostinger, etc.)

1. **Subir archivos por FTP**
   - Conectar a tu hosting via FTP
   - Subir todos los archivos a la carpeta public_html/

2. **Configurar permisos**
   ```
   Carpeta php/: 755
   Archivo proxy.php: 644
   ```

3. **Acceder desde tu dominio**
   - https://tudominio.com/

## ⚙️ Configuración

### Credenciales de Servidor

Editar en `js/auth.js` y `js/api.js`:

```javascript
// Cambiar la URL del servidor
const AUTH_CONFIG = {
    apiBase: 'http://TU-SERVIDOR:PUERTO',
    // ...
};
```

### Credenciales de Usuario

El sistema acepta estas credenciales por defecto:
- **Usuario:** Casa122, **Contraseña:** Panama21
- **Usuario:** demo, **Contraseña:** demo

Para agregar más usuarios, editar la función `authenticateUser()` en `js/auth.js`.

## 🔧 Solución de Problemas

### Error SSL/HTTPS

Si aparece error SSL al cargar desde HTTPS:

1. **Usar HTTP puro:**
   - Acceder via http://tudominio.com (sin 's')
   - Configurar hosting sin SSL forzado

2. **Usar proxy PHP:**
   - El archivo `php/proxy.php` maneja streams HTTP en HTTPS
   - Verificar que PHP esté habilitado en el hosting

### Error CORS

Si hay problemas de CORS:

1. **Configurar .htaccess:**
   ```apache
   Header always set Access-Control-Allow-Origin "*"
   Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
   Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
   ```

2. **Usar proxy PHP:**
   - El proxy maneja automáticamente CORS
   - Verificar que `php/proxy.php` sea accesible

### Streams no cargan

1. **Verificar URLs:**
   - Formato correcto: http://servidor:puerto/live/user/pass/id.ts
   - Verificar credenciales en localStorage

2. **Probar manualmente:**
   - Abrir URL de stream directamente en navegador
   - Verificar que el servidor IPTV esté funcionando

## 📱 Compatibilidad

### Navegadores Soportados
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Dispositivos
- PC/Mac (recomendado)
- Tablets
- Móviles (funcionalidad limitada)

### Reproductores
- Video.js (por defecto)
- HLS.js (para streams HLS)
- Clappr (alternativo)

## 🔐 Seguridad

### Recomendaciones

1. **Cambiar credenciales por defecto**
2. **Usar HTTPS cuando sea posible**
3. **Configurar firewall en servidor**
4. **Limitar acceso por IP si es necesario**

### Ofuscación

Las URLs están parcialmente ofuscadas en el código. Para mayor seguridad:

1. **Usar base64:**
   ```javascript
   apiBase: atob('aHR0cDovL3R1c2Vydmlkb3I6cHVlcnRv')
   ```

2. **Implementar autenticación JWT**
3. **Usar proxy con autenticación**

## 📞 Soporte

Si tienes problemas:

1. **Verificar consola del navegador** (F12)
2. **Revisar logs del servidor web**
3. **Probar con credenciales demo/demo**
4. **Verificar conectividad al servidor IPTV**

## 🎯 Características Principales

- ✅ Login con animación Call of Duty
- ✅ TV en vivo con EPG
- ✅ Películas por categorías  
- ✅ Series con temporadas/episodios
- ✅ Configuración personalizable
- ✅ Diseño responsivo
- ✅ Controles de teclado
- ✅ Picture-in-Picture
- ✅ Pantalla completa

## 🔄 Actualizaciones

Para actualizar:

1. **Hacer backup de configuración**
2. **Reemplazar archivos (excepto configuración)**
3. **Limpiar cache del navegador**
4. **Probar funcionalidad**
