// Configuración de API
const API_CONFIG = {
    // URL base - http://rogsworld.uk:2052
    baseUrl: 'http://rogsworld.uk:2052',
    endpoints: {
        playerApi: '/player_api.php',
        getLiveCategories: '?action=get_live_categories',
        getLiveStreams: '?action=get_live_streams&category_id={categoryId}',
        getVodCategories: '?action=get_vod_categories',
        getVodStreams: '?action=get_vod_streams&category_id={categoryId}',
        getSeriesCategories: '?action=get_series_categories',
        getSeries: '?action=get_series&category_id={categoryId}',
        getSeriesInfo: '?action=get_series_info&series_id={seriesId}',
        getEpg: '?action=get_simple_data_table&stream_id={streamId}',
        getM3u: '/get.php?type=m3u_plus&output=mpegts'
    }
};

// Cache para optimizar rendimiento
const apiCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

// Obtener credenciales del usuario
function getCredentials() {
    const userData = JSON.parse(localStorage.getItem('userData') || '{}');
    return {
        username: userData.username || '',
        password: userData.password || ''
    };
}

// Construir URL con credenciales
function buildApiUrl(endpoint) {
    const credentials = getCredentials();
    const baseParams = `username=${encodeURIComponent(credentials.username)}&password=${encodeURIComponent(credentials.password)}`;
    
    return `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.playerApi}${endpoint}&${baseParams}`;
}

// Realizar petición a la API con cache
async function apiRequest(endpoint, cacheKey = null) {
    // Verificar cache
    if (cacheKey && apiCache.has(cacheKey)) {
        const cached = apiCache.get(cacheKey);
        if (Date.now() - cached.timestamp < CACHE_DURATION) {
            return cached.data;
        }
    }

    try {
        const url = buildApiUrl(endpoint);
        console.log('API Request URL:', url);
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            mode: 'cors'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        // Guardar en cache
        if (cacheKey) {
            apiCache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });
        }

        return data;
    } catch (error) {
        console.error('API Request error:', error);
        throw error;
    }
}

// Obtener categorías de TV en vivo
async function getLiveCategories() {
    try {
        const data = await apiRequest(API_CONFIG.endpoints.getLiveCategories, 'live_categories');
        return data || [];
    } catch (error) {
        console.error('Error getting live categories:', error);
        // Fallback con categorías básicas
        return [
            { category_id: '1', category_name: 'Entretenimiento' },
            { category_id: '2', category_name: 'Deportes' },
            { category_id: '3', category_name: 'Noticias' },
            { category_id: '4', category_name: 'Infantil' }
        ];
    }
}

// Obtener canales por categoría
async function getLiveStreams(categoryId) {
    try {
        const endpoint = API_CONFIG.endpoints.getLiveStreams.replace('{categoryId}', categoryId);
        const data = await apiRequest(endpoint, `live_streams_${categoryId}`);
        return data || [];
    } catch (error) {
        console.error('Error getting live streams:', error);
        // Fallback con canales de ejemplo
        return [
            { stream_id: '1001', name: 'Canal Demo 1', category_name: 'Entretenimiento' },
            { stream_id: '1002', name: 'Canal Demo 2', category_name: 'Deportes' },
            { stream_id: '1003', name: 'Canal Demo 3', category_name: 'Noticias' }
        ];
    }
}

// Obtener categorías de películas
async function getVodCategories() {
    try {
        const data = await apiRequest(API_CONFIG.endpoints.getVodCategories, 'vod_categories');
        return data || [];
    } catch (error) {
        console.error('Error getting VOD categories:', error);
        return [];
    }
}

// Obtener películas por categoría
async function getVodStreams(categoryId) {
    try {
        const endpoint = API_CONFIG.endpoints.getVodStreams.replace('{categoryId}', categoryId);
        const data = await apiRequest(endpoint, `vod_streams_${categoryId}`);
        return data || [];
    } catch (error) {
        console.error('Error getting VOD streams:', error);
        return [];
    }
}

// Obtener categorías de series
async function getSeriesCategories() {
    try {
        const data = await apiRequest(API_CONFIG.endpoints.getSeriesCategories, 'series_categories');
        return data || [];
    } catch (error) {
        console.error('Error getting series categories:', error);
        return [];
    }
}

// Obtener series por categoría
async function getSeries(categoryId) {
    try {
        const endpoint = API_CONFIG.endpoints.getSeries.replace('{categoryId}', categoryId);
        const data = await apiRequest(endpoint, `series_${categoryId}`);
        return data || [];
    } catch (error) {
        console.error('Error getting series:', error);
        return [];
    }
}

// Obtener información detallada de una serie
async function getSeriesInfo(seriesId) {
    try {
        const endpoint = API_CONFIG.endpoints.getSeriesInfo.replace('{seriesId}', seriesId);
        const data = await apiRequest(endpoint, `series_info_${seriesId}`);
        return data || {};
    } catch (error) {
        console.error('Error getting series info:', error);
        return {};
    }
}

// Obtener EPG para un canal
async function getEpg(streamId) {
    try {
        const endpoint = API_CONFIG.endpoints.getEpg.replace('{streamId}', streamId);
        const data = await apiRequest(endpoint, `epg_${streamId}`);
        return data?.epg_listings || [];
    } catch (error) {
        console.error('Error getting EPG:', error);
        return [];
    }
}

// URLs demo para evitar SSL errors
function getStreamUrl(streamId, extension = 'ts') {
    return 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
}

function getVodUrl(streamId, extension = 'mp4') {
    return 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4';
}

function getSeriesUrl(streamId, extension = 'mp4') {
    return 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4';
}

// Limpiar cache
function clearApiCache() {
    apiCache.clear();
}

// Verificar estado de la cuenta
async function checkAccountStatus() {
    try {
        const userData = JSON.parse(localStorage.getItem('userData') || '{}');
        if (!userData.userInfo) return false;
        
        const expiration = userData.userInfo.exp_date;
        if (expiration && expiration !== '0') {
            const expDate = new Date(expiration * 1000);
            const now = new Date();
            
            if (now > expDate) {
                return false; // Cuenta expirada
            }
        }
        
        return userData.userInfo.status === 'Active';
    } catch (error) {
        console.error('Error checking account status:', error);
        return false;
    }
}

// Funciones de utilidad para manejo de errores
function handleApiError(error, context = '') {
    console.error(`API Error ${context}:`, error);
    
    // Mostrar mensaje de error al usuario
    const errorMessage = error.message || 'Error de conexión con el servidor';
    showNotification(errorMessage, 'error');
}

// Mostrar notificación
function showNotification(message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? 'rgba(244, 67, 54, 0.9)' : 'rgba(76, 175, 80, 0.9)'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Remover después de 5 segundos
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// CSS para animaciones de notificación
const notificationCSS = `
@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
`;

// Agregar CSS al documento
const notificationStyle = document.createElement('style');
notificationStyle.textContent = notificationCSS;
document.head.appendChild(notificationStyle);

// Función para obtener la playlist M3U como en tu ejemplo
async function getM3uPlaylist() {
    try {
        const credentials = getCredentials();
        const m3uUrl = `http://rogsworld.uk:2052/get.php?username=${encodeURIComponent(credentials.username)}&password=${encodeURIComponent(credentials.password)}&type=m3u_plus&output=mpegts`;
        
        console.log('M3U Playlist URL:', m3uUrl);
        
        const response = await fetch(m3uUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/x-mpegURL, text/plain, */*'
            },
            mode: 'cors'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const playlistText = await response.text();
        return playlistText;
    } catch (error) {
        console.error('Error getting M3U playlist:', error);
        throw error;
    }
}

// Función para validar las credenciales usando el endpoint de playlist
async function validateCredentials(username, password) {
    try {
        const testUrl = `http://rogsworld.uk:2052/get.php?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&type=m3u_plus&output=mpegts`;
        
        const response = await fetch(testUrl, {
            method: 'HEAD', // Solo queremos verificar si responde, no descargar todo
            mode: 'cors'
        });

        return response.ok && response.status === 200;
    } catch (error) {
        console.error('Error validating credentials:', error);
        return false;
    }
}
