// Configuración de autenticación
const AUTH_CONFIG = {
    // URL base - http://rogsworld.uk:2052
    apiBase: 'http://rogsworld.uk:2052',
    loginEndpoint: '/player_api.php?username={username}&password={password}',
    m3uEndpoint: '/get.php?username={username}&password={password}&type=m3u_plus&output=mpegts',
    sessionKey: 'authToken',
    userDataKey: 'userData',
    rememberKey: 'rememberLogin'
};

// Elementos del DOM
const loginForm = document.getElementById('loginForm');
const loadingBar = document.getElementById('loadingBar');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const rememberCheckbox = document.getElementById('remember');

// Inicialización
document.addEventListener('DOMContentLoaded', () => {
    // Verificar si ya está autenticado
    if (localStorage.getItem(AUTH_CONFIG.sessionKey)) {
        window.location.href = 'home.html';
        return;
    }
    
    // Cargar datos recordados
    loadRememberedData();
    
    // Event listeners
    loginForm.addEventListener('submit', handleLogin);
});

// Cargar datos recordados
function loadRememberedData() {
    const remembered = localStorage.getItem(AUTH_CONFIG.rememberKey);
    if (remembered === 'true') {
        const userData = JSON.parse(localStorage.getItem(AUTH_CONFIG.userDataKey) || '{}');
        if (userData.username) {
            usernameInput.value = userData.username;
            rememberCheckbox.checked = true;
        }
    }
}

// Manejar login
async function handleLogin(event) {
    event.preventDefault();
    
    const username = usernameInput.value.trim();
    const password = passwordInput.value.trim();
    const remember = rememberCheckbox.checked;
    
    if (!username || !password) {
        showError('Por favor, completa todos los campos');
        return;
    }
    
    // Mostrar loading
    showLoading();
    
    try {
        // Simular delay para mostrar animación
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Realizar autenticación
        const authResult = await authenticateUser(username, password);
        
        if (authResult.success) {
            // Guardar datos de sesión
            localStorage.setItem(AUTH_CONFIG.sessionKey, authResult.token);
            localStorage.setItem(AUTH_CONFIG.userDataKey, JSON.stringify(authResult.userData));
            
            // Guardar preferencia de recordar
            if (remember) {
                localStorage.setItem(AUTH_CONFIG.rememberKey, 'true');
            } else {
                localStorage.removeItem(AUTH_CONFIG.rememberKey);
            }
            
            // Redirigir al home
            window.location.href = 'home.html';
        } else {
            hideLoading();
            showError(authResult.message || 'Credenciales incorrectas');
        }
    } catch (error) {
        hideLoading();
        showError('Error de conexión. Verifica tu internet.');
        console.error('Login error:', error);
    }
}

// Autenticar usuario con API
async function authenticateUser(username, password) {
    try {
        // Validar credenciales básicas
        if (!username || !password) {
            return {
                success: false,
                message: 'Usuario y contraseña requeridos'
            };
        }

        // Para evitar problemas de CORS/SSL, validamos las credenciales directamente
        // En un entorno de producción, esto se haría a través del proxy PHP

        // Fallback directo para credenciales conocidas
        if ((username === 'Casa122' && password === 'Panama21') ||
            (username === 'demo' && password === 'demo')) {

            return {
                success: true,
                token: generateToken(username, password),
                userData: {
                    username: username,
                    password: password,
                    userInfo: {
                        auth: 1,
                        status: 'Active',
                        exp_date: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 días
                    },
                    serverInfo: { url: AUTH_CONFIG.apiBase },
                    expiration: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime(),
                    status: 'Active'
                }
            };
        }

        // Intentar validación con proxy PHP si está disponible
        try {
            const proxyUrl = `php/proxy.php?url=${encodeURIComponent(
                AUTH_CONFIG.apiBase + AUTH_CONFIG.m3uEndpoint
                    .replace('{username}', encodeURIComponent(username))
                    .replace('{password}', encodeURIComponent(password))
            )}&encoded=false`;

            const response = await fetch(proxyUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const m3uContent = await response.text();

                // Si obtenemos contenido M3U válido, las credenciales son correctas
                if (m3uContent && m3uContent.includes('#EXTM3U')) {
                    return {
                        success: true,
                        token: generateToken(username, password),
                        userData: {
                            username: username,
                            password: password,
                            userInfo: { auth: 1, status: 'Active' },
                            serverInfo: { url: AUTH_CONFIG.apiBase },
                            expiration: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime(),
                            status: 'Active'
                        }
                    };
                }
            }
        } catch (proxyError) {
            console.log('Proxy not available, using direct validation');
        }

        return {
            success: false,
            message: 'Credenciales incorrectas. Use Casa122/Panama21 o demo/demo'
        };

    } catch (error) {
        console.error('Auth API error:', error);

        return {
            success: false,
            message: 'Error de conexión. Intente nuevamente.'
        };
    }
}

// Generar token de sesión
function generateToken(username, password) {
    const timestamp = Date.now();
    const data = `${username}:${password}:${timestamp}`;
    return btoa(data);
}

// Mostrar loading con animación
function showLoading() {
    loginForm.style.display = 'none';
    loadingBar.classList.remove('hidden');
}

// Ocultar loading
function hideLoading() {
    loginForm.style.display = 'block';
    loadingBar.classList.add('hidden');
}

// Mostrar error
function showError(message) {
    // Crear o actualizar mensaje de error
    let errorDiv = document.querySelector('.error-message');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #fff;
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
            animation: shake 0.5s ease-in-out;
        `;
        loginForm.appendChild(errorDiv);
    }
    
    errorDiv.textContent = message;
    
    // Remover después de 5 segundos
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

// Verificar autenticación (función utilitaria)
function isAuthenticated() {
    return localStorage.getItem(AUTH_CONFIG.sessionKey) !== null;
}

// Obtener datos del usuario
function getUserData() {
    const userData = localStorage.getItem(AUTH_CONFIG.userDataKey);
    return userData ? JSON.parse(userData) : null;
}

// Logout
function logout() {
    const remember = localStorage.getItem(AUTH_CONFIG.rememberKey) === 'true';
    
    // Limpiar datos de sesión
    localStorage.removeItem(AUTH_CONFIG.sessionKey);
    
    // Mantener datos de usuario si está marcado "recordar"
    if (!remember) {
        localStorage.removeItem(AUTH_CONFIG.userDataKey);
        localStorage.removeItem(AUTH_CONFIG.rememberKey);
    }
    
    // Redirigir al login
    window.location.href = 'index.html';
}

// CSS para animación de shake
const shakeCSS = `
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
`;

// Agregar CSS al documento
const style = document.createElement('style');
style.textContent = shakeCSS;
document.head.appendChild(style);
