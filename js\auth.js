// Configuración de autenticación
const AUTH_CONFIG = {
    // Usar proxy PHP para evitar Mixed Content en hosting HTTPS
    apiBase: 'php/proxy.php',
    serverUrl: 'http://rogsworld.uk:2052',
    loginEndpoint: '/player_api.php?username={username}&password={password}',
    m3uEndpoint: '/get.php?username={username}&password={password}&type=m3u_plus&output=mpegts',
    sessionKey: 'authToken',
    userDataKey: 'userData',
    rememberKey: 'rememberLogin'
};

// Elementos del DOM
const loginForm = document.getElementById('loginForm');
const loadingBar = document.getElementById('loadingBar');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const rememberCheckbox = document.getElementById('remember');

// Inicialización
document.addEventListener('DOMContentLoaded', () => {
    // Verificar si ya está autenticado
    if (localStorage.getItem(AUTH_CONFIG.sessionKey)) {
        window.location.href = 'home.html';
        return;
    }
    
    // Cargar datos recordados
    loadRememberedData();
    
    // Event listeners
    loginForm.addEventListener('submit', handleLogin);
});

// Cargar datos recordados
function loadRememberedData() {
    const remembered = localStorage.getItem(AUTH_CONFIG.rememberKey);
    if (remembered === 'true') {
        const userData = JSON.parse(localStorage.getItem(AUTH_CONFIG.userDataKey) || '{}');
        if (userData.username) {
            usernameInput.value = userData.username;
            rememberCheckbox.checked = true;
        }
    }
}

// Manejar login
async function handleLogin(event) {
    event.preventDefault();
    
    const username = usernameInput.value.trim();
    const password = passwordInput.value.trim();
    const remember = rememberCheckbox.checked;
    
    if (!username || !password) {
        showError('Por favor, completa todos los campos');
        return;
    }
    
    // Mostrar loading
    showLoading();
    
    try {
        // Simular delay para mostrar animación
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Realizar autenticación
        const authResult = await authenticateUser(username, password);
        
        if (authResult.success) {
            // Guardar datos de sesión
            localStorage.setItem(AUTH_CONFIG.sessionKey, authResult.token);
            localStorage.setItem(AUTH_CONFIG.userDataKey, JSON.stringify(authResult.userData));
            
            // Guardar preferencia de recordar
            if (remember) {
                localStorage.setItem(AUTH_CONFIG.rememberKey, 'true');
            } else {
                localStorage.removeItem(AUTH_CONFIG.rememberKey);
            }
            
            // Redirigir al home
            window.location.href = 'home.html';
        } else {
            hideLoading();
            showError(authResult.message || 'Credenciales incorrectas');
        }
    } catch (error) {
        hideLoading();
        showError('Error de conexión. Verifica tu internet.');
        console.error('Login error:', error);
    }
}

// Autenticar usuario con API
async function authenticateUser(username, password) {
    try {
        // Usar proxy PHP para evitar Mixed Content
        const targetUrl = `${AUTH_CONFIG.serverUrl}/player_api.php?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;
        const proxyUrl = `${AUTH_CONFIG.apiBase}?url=${encodeURIComponent(targetUrl)}`;

        console.log('Attempting authentication via proxy:', proxyUrl);

        const response = await fetch(proxyUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const authData = await response.json();
            console.log('Auth API response:', authData);

            // Verificar si la autenticación fue exitosa
            if (authData && authData.user_info && authData.user_info.auth === 1) {
                return {
                    success: true,
                    token: generateToken(username, password),
                    userData: {
                        username: username,
                        password: password,
                        userInfo: authData.user_info,
                        serverInfo: authData.server_info || { url: AUTH_CONFIG.apiBase },
                        expiration: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime(),
                        status: authData.user_info.status || 'Active'
                    }
                };
            }
        }

        // Segundo intento: validar usando el endpoint M3U via proxy
        console.log('Trying M3U endpoint validation via proxy...');
        const m3uTargetUrl = `${AUTH_CONFIG.serverUrl}/get.php?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&type=m3u_plus&output=mpegts`;
        const m3uProxyUrl = `${AUTH_CONFIG.apiBase}?url=${encodeURIComponent(m3uTargetUrl)}`;

        const m3uResponse = await fetch(m3uProxyUrl, {
            method: 'GET'
        });

        if (m3uResponse.ok) {
            const m3uContent = await m3uResponse.text();
            if (m3uContent && m3uContent.includes('#EXTM3U')) {
                console.log('M3U endpoint validation successful');
                return {
                    success: true,
                    token: generateToken(username, password),
                    userData: {
                        username: username,
                        password: password,
                        userInfo: {
                            auth: 1,
                            status: 'Active',
                            exp_date: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60)
                        },
                        serverInfo: { url: AUTH_CONFIG.serverUrl },
                        expiration: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime(),
                        status: 'Active'
                    }
                };
            }
        }

        return {
            success: false,
            message: 'Credenciales incorrectas o cuenta inactiva'
        };

    } catch (error) {
        console.error('Auth API error:', error);
        
        // Fallback para credenciales de demostración cuando la API no esté disponible
        if ((username === 'Casa122' && password === 'Panama21') ||
            (username === 'demo' && password === 'demo')) {

            console.log('Using fallback authentication for demo credentials');
            return {
                success: true,
                token: generateToken(username, password),
                userData: {
                    username: username,
                    password: password,
                    userInfo: {
                        auth: 1,
                        status: 'Active',
                        exp_date: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60)
                    },
                    serverInfo: { url: AUTH_CONFIG.apiBase },
                    expiration: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime(),
                    status: 'Active'
                }
            };
        }

        return {
            success: false,
            message: 'Error de conexión. Verifica la conexión a internet.'
        };
    }
}

// Generar token de sesión
function generateToken(username, password) {
    const timestamp = Date.now();
    const data = `${username}:${password}:${timestamp}`;
    return btoa(data);
}

// Mostrar loading con animación
function showLoading() {
    loginForm.style.display = 'none';
    loadingBar.classList.remove('hidden');
}

// Ocultar loading
function hideLoading() {
    loginForm.style.display = 'block';
    loadingBar.classList.add('hidden');
}

// Mostrar error
function showError(message) {
    // Crear o actualizar mensaje de error
    let errorDiv = document.querySelector('.error-message');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #fff;
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
            animation: shake 0.5s ease-in-out;
        `;
        loginForm.appendChild(errorDiv);
    }
    
    errorDiv.textContent = message;
    
    // Remover después de 5 segundos
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

// Verificar autenticación (función utilitaria)
function isAuthenticated() {
    return localStorage.getItem(AUTH_CONFIG.sessionKey) !== null;
}

// Obtener datos del usuario
function getUserData() {
    const userData = localStorage.getItem(AUTH_CONFIG.userDataKey);
    return userData ? JSON.parse(userData) : null;
}

// Logout
function logout() {
    const remember = localStorage.getItem(AUTH_CONFIG.rememberKey) === 'true';
    
    // Limpiar datos de sesión
    localStorage.removeItem(AUTH_CONFIG.sessionKey);
    
    // Mantener datos de usuario si está marcado "recordar"
    if (!remember) {
        localStorage.removeItem(AUTH_CONFIG.userDataKey);
        localStorage.removeItem(AUTH_CONFIG.rememberKey);
    }
    
    // Redirigir al login
    window.location.href = 'index.html';
}

// CSS para animación de shake
const shakeCSS = `
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
`;

// Agregar CSS al documento
const style = document.createElement('style');
style.textContent = shakeCSS;
document.head.appendChild(style);
