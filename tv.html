<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RGSMDIA Player - TV en Vivo</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="tv-body">
    <header class="header">
        <div class="header-content">
            <button id="backBtn" class="back-btn">← Volver</button>
            <h1>TV en Vivo</h1>
        </div>
    </header>
    
    <div class="tv-layout">
        <aside class="sidebar">
            <h3>Categorías</h3>
            <div id="categoriesList" class="categories-list">
                <!-- Categorías se cargan dinámicamente -->
            </div>
        </aside>
        
        <main class="content">
            <div class="player-section">
                <div id="videoPlayer" class="video-player">
                    <video id="mainVideo" controls>
                        Tu navegador no soporta el elemento video.
                    </video>
                </div>
                
                <div class="player-controls">
                    <button id="fullscreenBtn">🔳 Pantalla Completa</button>
                    <button id="pipBtn">📺 Picture-in-Picture</button>
                    <div class="volume-control">
                        <span>🔊</span>
                        <input type="range" id="volumeSlider" min="0" max="100" value="50">
                    </div>
                </div>
            </div>
            
            <div class="channels-section">
                <h3 id="categoryTitle">Selecciona una categoría</h3>
                <div id="channelsList" class="channels-grid">
                    <!-- Canales se cargan dinámicamente -->
                </div>
            </div>
            
            <div class="epg-section">
                <h3>Guía de Programación</h3>
                <div id="epgContainer" class="epg-container">
                    <!-- EPG se carga dinámicamente -->
                </div>
            </div>
        </main>
    </div>
    
    <script src="js/api.js"></script>
    <script src="js/player.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Verificar autenticación
        if (!localStorage.getItem('authToken')) {
            window.location.href = 'index.html';
        }
        
        // Navegación
        document.getElementById('backBtn').addEventListener('click', () => {
            window.location.href = 'home.html';
        });
        
        // Inicializar página
        document.addEventListener('DOMContentLoaded', () => {
            loadLiveCategories();
            initializePlayer();
        });

        // Cargar categorías de TV
        async function loadLiveCategories() {
            try {
                const categories = await getLiveCategories();
                displayCategories(categories);
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        // Mostrar categorías
        function displayCategories(categories) {
            const container = document.getElementById('categoriesList');
            if (!container) return;

            container.innerHTML = categories.map(cat => `
                <div class="category-item" data-id="${cat.category_id}" onclick="loadChannels(${cat.category_id}, '${cat.category_name}')">
                    ${cat.category_name}
                </div>
            `).join('');
        }

        // Cargar canales por categoría
        async function loadChannels(categoryId, categoryName) {
            try {
                document.getElementById('categoryTitle').textContent = categoryName;

                // Marcar categoría activa
                document.querySelectorAll('.category-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-id="${categoryId}"]`).classList.add('active');

                const channels = await getLiveStreams(categoryId);
                displayChannels(channels);
            } catch (error) {
                console.error('Error loading channels:', error);
            }
        }

        // Mostrar canales
        function displayChannels(channels) {
            const container = document.getElementById('channelsList');
            if (!container) return;

            container.innerHTML = channels.map(channel => `
                <div class="channel-item" onclick="playChannel(${channel.stream_id}, '${channel.name}')">
                    <div class="channel-logo">📺</div>
                    <div class="channel-name">${channel.name}</div>
                    <div class="channel-category">${channel.category_name || ''}</div>
                </div>
            `).join('');
        }

        // Reproducir canal
        function playChannel(streamId, channelName) {
            const streamUrl = getStreamUrl(streamId);
            playStream(streamUrl, channelName, streamId);
        }
    </script>
</body>
</html>
