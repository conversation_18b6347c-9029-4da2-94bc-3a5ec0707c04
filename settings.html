<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RGSMDIA Player - Ajustes</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="settings-body">
    <header class="header">
        <div class="header-content">
            <button id="backBtn" class="back-btn">← Volver</button>
            <h1>Ajustes</h1>
        </div>
    </header>
    
    <main class="settings-content">
        <div class="settings-container">
            <div class="settings-section">
                <h3>Reproductor</h3>
                <div class="setting-item">
                    <label for="playerType">Tipo de reproductor:</label>
                    <select id="playerType">
                        <option value="video.js">Video.js</option>
                        <option value="hls.js">HLS.js</option>
                        <option value="clappr">Clappr</option>
                    </select>
                </div>
            </div>
            
            <div class="settings-section">
                <h3>Interfaz</h3>
                <div class="setting-item">
                    <label for="animationType">Tipo de animación:</label>
                    <select id="animationType">
                        <option value="smooth">Suave</option>
                        <option value="fast">Rápida</option>
                        <option value="none">Sin animaciones</option>
                    </select>
                </div>
            </div>
            
            <div class="settings-section">
                <h3>Rendimiento</h3>
                <div class="setting-item">
                    <label for="cacheEnabled">Mejorar velocidad (cache):</label>
                    <input type="checkbox" id="cacheEnabled">
                </div>
                <div class="setting-item">
                    <label for="prefetchEnabled">Precarga de contenido:</label>
                    <input type="checkbox" id="prefetchEnabled">
                </div>
            </div>
            
            <div class="settings-section">
                <h3>Audio y Subtítulos</h3>
                <div class="setting-item">
                    <label for="forceSpanish">Forzar audio español latino:</label>
                    <input type="checkbox" id="forceSpanish">
                </div>
                <div class="setting-item">
                    <label for="forceSubtitles">Forzar subtítulos (si disponibles):</label>
                    <input type="checkbox" id="forceSubtitles">
                </div>
            </div>
            
            <div class="settings-section">
                <h3>Información de Cuenta</h3>
                <div class="info-item">
                    <span>Usuario:</span>
                    <span id="userInfo">-</span>
                </div>
                <div class="info-item">
                    <span>Fecha de expiración:</span>
                    <span id="expirationInfo">-</span>
                </div>
                <div class="info-item">
                    <span>Estado:</span>
                    <span id="statusInfo">-</span>
                </div>
            </div>
            
            <div class="settings-actions">
                <button id="saveSettings" class="save-btn">Guardar Configuración</button>
                <button id="resetSettings" class="reset-btn">Restablecer</button>
            </div>
        </div>
    </main>
    
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Verificar autenticación
        if (!localStorage.getItem('authToken')) {
            window.location.href = 'index.html';
        }
        
        // Navegación
        document.getElementById('backBtn').addEventListener('click', () => {
            window.location.href = 'home.html';
        });
        
        // Inicializar página
        document.addEventListener('DOMContentLoaded', () => {
            loadSettings();
            loadAccountInfo();
        });

        // Cargar configuración guardada
        function loadSettings() {
            const settings = JSON.parse(localStorage.getItem('playerSettings') || '{}');

            // Configurar valores por defecto
            document.getElementById('playerType').value = settings.playerType || 'video.js';
            document.getElementById('animationType').value = settings.animationType || 'smooth';
            document.getElementById('cacheEnabled').checked = settings.cacheEnabled || false;
            document.getElementById('prefetchEnabled').checked = settings.prefetchEnabled || false;
            document.getElementById('forceSpanish').checked = settings.forceSpanish || false;
            document.getElementById('forceSubtitles').checked = settings.forceSubtitles || false;
        }

        // Cargar información de la cuenta
        async function loadAccountInfo() {
            try {
                const userData = JSON.parse(localStorage.getItem('userData') || '{}');

                document.getElementById('userInfo').textContent = userData.username || 'N/A';

                if (userData.userInfo) {
                    const expiration = userData.userInfo.exp_date;
                    if (expiration && expiration !== '0') {
                        const expDate = new Date(expiration * 1000);
                        document.getElementById('expirationInfo').textContent = expDate.toLocaleDateString('es-ES');
                    } else {
                        document.getElementById('expirationInfo').textContent = 'Sin límite';
                    }

                    document.getElementById('statusInfo').textContent = userData.userInfo.status || 'Activo';
                } else {
                    document.getElementById('expirationInfo').textContent = 'N/A';
                    document.getElementById('statusInfo').textContent = 'N/A';
                }
            } catch (error) {
                console.error('Error loading account info:', error);
            }
        }

        // Guardar configuración
        function saveSettings() {
            const settings = {
                playerType: document.getElementById('playerType').value,
                animationType: document.getElementById('animationType').value,
                cacheEnabled: document.getElementById('cacheEnabled').checked,
                prefetchEnabled: document.getElementById('prefetchEnabled').checked,
                forceSpanish: document.getElementById('forceSpanish').checked,
                forceSubtitles: document.getElementById('forceSubtitles').checked,
                volume: JSON.parse(localStorage.getItem('playerSettings') || '{}').volume || 0.5
            };

            localStorage.setItem('playerSettings', JSON.stringify(settings));
            showNotification('Configuración guardada correctamente', 'success');
        }

        // Restablecer configuración
        function resetSettings() {
            showConfirmation(
                '¿Estás seguro de que quieres restablecer toda la configuración?',
                () => {
                    localStorage.removeItem('playerSettings');
                    loadSettings();
                    showNotification('Configuración restablecida', 'success');
                }
            );
        }

        // Event listeners
        document.getElementById('saveSettings').addEventListener('click', saveSettings);
        document.getElementById('resetSettings').addEventListener('click', resetSettings);
    </script>
</body>
</html>
