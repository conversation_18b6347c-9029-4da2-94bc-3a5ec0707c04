<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy Test - Video Streaming</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        video {
            width: 100%;
            max-width: 600px;
            height: 300px;
            background: #000;
            margin: 15px 0;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test de Proxy para Videos</h1>
        <p>Esta página prueba si el proxy PHP puede servir videos correctamente.</p>
        
        <div class="input-group">
            <label for="testStreamId">Stream ID:</label>
            <input type="text" id="testStreamId" value="1736896" placeholder="ID del stream a probar">
        </div>
        
        <button class="test-button" onclick="testStreamUrl()">Test Stream URL Generation</button>
        <button class="test-button" onclick="testProxyResponse()">Test Proxy Response</button>
        <button class="test-button" onclick="testVideoLoad()">Test Video Load</button>
        
        <div id="results"></div>
        
        <h3>Video Test Player</h3>
        <video id="testVideo" controls>
            Tu navegador no soporta el elemento video.
        </video>
        
        <button class="test-button" onclick="loadTestVideo()">Load Test Stream</button>
    </div>

    <script>
        // Configuración similar a la del proyecto
        const API_CONFIG = {
            proxyUrl: 'php/proxy.php',
            baseUrl: 'http://rogsworld.uk:2052'
        };

        function showResult(message, isSuccess = false) {
            const results = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            results.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function getCredentials() {
            // Usar credenciales hardcoded para test
            return {
                username: 'Casa122',
                password: 'Panama21'
            };
        }

        function testStreamUrl() {
            clearResults();
            const streamId = document.getElementById('testStreamId').value;
            const credentials = getCredentials();
            
            // Generar URL como lo hace el sistema real
            const streamUrl = `${API_CONFIG.baseUrl}/live/${credentials.username}/${credentials.password}/${streamId}.ts`;
            const proxyUrl = `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(streamUrl)}`;
            
            showResult(`Stream ID: ${streamId}`);
            showResult(`Original Stream URL: ${streamUrl}`);
            showResult(`Proxy Stream URL: ${proxyUrl}`);
            showResult(`Generated successfully!`, true);
        }

        async function testProxyResponse() {
            const streamId = document.getElementById('testStreamId').value;
            const credentials = getCredentials();
            
            const streamUrl = `${API_CONFIG.baseUrl}/live/${credentials.username}/${credentials.password}/${streamId}.ts`;
            const proxyUrl = `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(streamUrl)}`;
            
            showResult(`Testing proxy response for: ${proxyUrl}`);
            
            try {
                const response = await fetch(proxyUrl, {
                    method: 'HEAD' // Solo verificar headers
                });
                
                showResult(`Response Status: ${response.status} ${response.statusText}`);
                showResult(`Content-Type: ${response.headers.get('Content-Type')}`);
                showResult(`Content-Length: ${response.headers.get('Content-Length')}`);
                
                if (response.ok) {
                    showResult(`Proxy response OK!`, true);
                } else {
                    showResult(`Proxy response failed: ${response.status}`);
                }
            } catch (error) {
                showResult(`Proxy test error: ${error.message}`);
                console.error('Proxy test error:', error);
            }
        }

        async function testVideoLoad() {
            const streamId = document.getElementById('testStreamId').value;
            const credentials = getCredentials();
            
            const streamUrl = `${API_CONFIG.baseUrl}/live/${credentials.username}/${credentials.password}/${streamId}.ts`;
            const proxyUrl = `${API_CONFIG.proxyUrl}?url=${encodeURIComponent(streamUrl)}`;
            
            showResult(`Testing video load with: ${proxyUrl}`);
            
            const video = document.getElementById('testVideo');
            
            video.addEventListener('loadstart', () => {
                showResult(`Video: Load started`, true);
            });
            
            video.addEventListener('loadeddata', () => {
                showResult(`Video: Data loaded successfully!`, true);
            });
            
            video.addEventListener('error', (e) => {
                showResult(`Video Error: ${e.type}`);
                showResult(`Network State: ${video.networkState}`);
                showResult(`Ready State: ${video.readyState}`);
                showResult(`Current Src: ${video.currentSrc}`);
                if (video.error) {
                    showResult(`Error Code: ${video.error.code}`);
                    showResult(`Error Message: ${video.error.message}`);
                }
            });
            
            video.src = proxyUrl;
        }

        function loadTestVideo() {
            testVideoLoad();
        }

        // Auto-load test on page load
        window.addEventListener('DOMContentLoaded', () => {
            showResult("Página cargada. Prueba los diferentes botones para diagnosticar el proxy.");
        });
    </script>
</body>
</html>
